"""
Admin service for administrative operations
Maintains identical functionality to PHP version
"""

import logging
from typing import Optional, Dict, Any, List
from config.database import get_collection, COLLECTIONS
from config.settings import settings
from utils.helpers import get_current_timestamp, get_all_admin_ids, send_safe_message

logger = logging.getLogger(__name__)

class AdminService:
    """Service for admin-related operations"""
    
    async def get_admin_settings(self, admin_id: int = None) -> Dict[str, Any]:
        """Get admin settings"""
        try:
            if admin_id is None:
                admin_id = settings.ADMIN_ID
            
            collection = await get_collection(COLLECTIONS['admin_settings'])
            admin_settings = await collection.find_one({"admin_id": admin_id})
            
            if not admin_settings:
                # Create default admin settings
                admin_settings = await self._create_default_admin_settings(admin_id)
            
            return admin_settings
            
        except Exception as e:
            logger.error(f"Error getting admin settings for {admin_id}: {e}")
            return {}
    
    async def _create_default_admin_settings(self, admin_id: int) -> Dict[str, Any]:
        """Create default admin settings"""
        try:
            collection = await get_collection(COLLECTIONS['admin_settings'])
            
            default_settings = {
                "admin_id": admin_id,
                "main_channel": "",
                "private_logs_channel": "",
                "maintenance_status": "Off",
                "otp_website_api_key": "",
                "per_refer_amount": 0,
                "joining_bonus_amount": 0,
                "gift_channel": "",
                "gift_amount": 0,
                "force_sub_channels": [],
                "level_rewards_enabled": True,
                "level_rewards_config": {
                    "referral_requirements": [1, 5, 10, 15, 20, 25],
                    "bonus_amounts": [2, 10, 15, 20, 25, 30]
                },
                "per_refer_amount_range": "1-5",
                "joining_bonus_amount_range": "4-6",
                "withdrawal_settings": {
                    "enabled": True,
                    "tax_type": "none",
                    "tax_amount": 0,
                    "tax_percentage": 0
                },
                # Bot is permanently enabled
                "bot_enabled": True,
                "unique_bank_usdt_enforcement": False,
                "withdrawal_ban_list": [],
                # Dynamic admin management
                "admin_list": [],
                "created_at": get_current_timestamp(),
                "updated_at": get_current_timestamp()
            }
            
            await collection.insert_one(default_settings)
            return default_settings
            
        except Exception as e:
            logger.error(f"Error creating default admin settings: {e}")
            return {}

    async def get_admin_setting(self, field: str, admin_id: int = None) -> Any:
        """Get a specific admin setting"""
        try:
            admin_settings = await self.get_admin_settings(admin_id)
            return admin_settings.get(field)

        except Exception as e:
            logger.error(f"Error getting admin setting {field}: {e}")
            return None

    async def update_admin_setting(self, field: str, value: Any, admin_id: int = None) -> bool:
        """Update admin setting"""
        try:
            if admin_id is None:
                admin_id = settings.ADMIN_ID

            logger.info(f"Updating admin setting: field={field}, value={value}, admin_id={admin_id}")

            collection = await get_collection(COLLECTIONS['admin_settings'])
            logger.info("Got admin_settings collection")

            # Ensure admin settings exist
            logger.info("Ensuring admin settings exist...")
            admin_settings = await self.get_admin_settings(admin_id)
            logger.info(f"Admin settings exist: {admin_settings is not None}")

            logger.info(f"Executing update query for admin_id={admin_id}")
            result = await collection.update_one(
                {"admin_id": admin_id},
                {"$set": {
                    field: value,
                    "updated_at": get_current_timestamp()
                }}
            )

            logger.info(f"Update result: matched_count={result.matched_count}, modified_count={result.modified_count}")

            success = result.modified_count > 0
            logger.info(f"Update success: {success}")
            return success

        except Exception as e:
            logger.error(f"Error updating admin setting {field}: {e}")
            import traceback
            logger.error(f"Full traceback: {traceback.format_exc()}")
            return False
    
    async def add_force_sub_channel(
        self, 
        channel_id: int, 
        title: str, 
        username: str, 
        added_by: int,
        admin_id: int = None
    ) -> bool:
        """Add force subscription channel"""
        try:
            if admin_id is None:
                admin_id = settings.ADMIN_ID
            
            collection = await get_collection(COLLECTIONS['admin_settings'])
            
            channel_data = {
                "id": channel_id,
                "title": title,
                "username": username,
                "type": "force_sub",
                "added_by": added_by,
                "added_at": get_current_timestamp()
            }
            
            result = await collection.update_one(
                {"admin_id": admin_id},
                {
                    "$push": {"force_sub_channels": channel_data},
                    "$set": {"updated_at": get_current_timestamp()}
                }
            )
            
            return result.modified_count > 0
            
        except Exception as e:
            logger.error(f"Error adding force sub channel: {e}")
            return False
    
    async def remove_force_sub_channel(self, channel_id: int, admin_id: int = None) -> bool:
        """Remove force subscription channel"""
        try:
            if admin_id is None:
                admin_id = settings.ADMIN_ID
            
            collection = await get_collection(COLLECTIONS['admin_settings'])
            
            result = await collection.update_one(
                {"admin_id": admin_id},
                {
                    "$pull": {"force_sub_channels": {"id": channel_id}},
                    "$set": {"updated_at": get_current_timestamp()}
                }
            )
            
            return result.modified_count > 0
            
        except Exception as e:
            logger.error(f"Error removing force sub channel: {e}")
            return False
    
    async def get_force_sub_channels(self, admin_id: int = None) -> List[Dict[str, Any]]:
        """Get force subscription channels"""
        try:
            admin_settings = await self.get_admin_settings(admin_id)
            return admin_settings.get('force_sub_channels', [])
            
        except Exception as e:
            logger.error(f"Error getting force sub channels: {e}")
            return []
    
    async def update_bot_info(self, username: str, first_name: str) -> bool:
        """Update bot information"""
        try:
            collection = await get_collection(COLLECTIONS['bot_info'])
            
            bot_info = {
                "username": username,
                "first_name": first_name,
                "updated_at": get_current_timestamp()
            }
            
            result = await collection.update_one(
                {},
                {"$set": bot_info},
                upsert=True
            )
            
            return result.modified_count > 0 or result.upserted_id is not None
            
        except Exception as e:
            logger.error(f"Error updating bot info: {e}")
            return False
    
    async def get_bot_statistics(self) -> Dict[str, Any]:
        """Get bot statistics"""
        try:
            users_collection = await get_collection(COLLECTIONS['users'])
            
            # Total users
            total_users = await users_collection.count_documents({})
            
            # Banned users
            banned_users = await users_collection.count_documents({"banned": True})
            
            # Active users (not banned)
            active_users = total_users - banned_users
            
            # Users with referrals
            users_with_referrals = await users_collection.count_documents({
                "promotion_report": {"$exists": True, "$ne": []}
            })
            
            # Total balance across all users
            pipeline = [
                {"$group": {
                    "_id": None,
                    "total_balance": {"$sum": "$balance"},
                    "total_successful_withdrawals": {"$sum": "$successful_withdraw"},
                    "total_pending_withdrawals": {"$sum": "$withdraw_under_review"}
                }}
            ]
            
            cursor = users_collection.aggregate(pipeline)
            financial_data = await cursor.to_list(length=1)
            
            if financial_data:
                financial = financial_data[0]
            else:
                financial = {
                    "total_balance": 0,
                    "total_successful_withdrawals": 0,
                    "total_pending_withdrawals": 0
                }
            
            # Total referrals
            referral_pipeline = [
                {"$unwind": {"path": "$promotion_report", "preserveNullAndEmptyArrays": True}},
                {"$group": {
                    "_id": None,
                    "total_referrals": {"$sum": 1},
                    "total_referral_earnings": {"$sum": "$promotion_report.amount_got"}
                }}
            ]
            
            cursor = users_collection.aggregate(referral_pipeline)
            referral_data = await cursor.to_list(length=1)
            
            if referral_data:
                referral_stats = referral_data[0]
            else:
                referral_stats = {
                    "total_referrals": 0,
                    "total_referral_earnings": 0
                }
            
            return {
                "total_users": total_users,
                "active_users": active_users,
                "banned_users": banned_users,
                "users_with_referrals": users_with_referrals,
                "total_balance": financial["total_balance"],
                "total_successful_withdrawals": financial["total_successful_withdrawals"],
                "total_pending_withdrawals": financial["total_pending_withdrawals"],
                "total_referrals": referral_stats["total_referrals"],
                "total_referral_earnings": referral_stats["total_referral_earnings"]
            }
            
        except Exception as e:
            logger.error(f"Error getting bot statistics: {e}")
            return {}
    
    async def get_financial_overview(self) -> Dict[str, Any]:
        """Get financial overview"""
        try:
            users_collection = await get_collection(COLLECTIONS['users'])
            
            pipeline = [
                {
                    "$group": {
                        "_id": None,
                        "total_user_balance": {"$sum": "$balance"},
                        "total_successful_withdrawals": {"$sum": "$successful_withdraw"},
                        "total_pending_withdrawals": {"$sum": "$withdraw_under_review"},
                        "total_joining_bonuses": {"$sum": "$joining_bonus_got"},
                        "users_with_withdrawals": {
                            "$sum": {"$cond": [{"$gt": ["$successful_withdraw", 0]}, 1, 0]}
                        },
                        "users_with_pending": {
                            "$sum": {"$cond": [{"$gt": ["$withdraw_under_review", 0]}, 1, 0]}
                        }
                    }
                }
            ]
            
            cursor = users_collection.aggregate(pipeline)
            result = await cursor.to_list(length=1)
            
            if result:
                data = result[0]
                
                # Calculate total money distributed
                total_distributed = (
                    data["total_successful_withdrawals"] + 
                    data["total_user_balance"] + 
                    data["total_pending_withdrawals"]
                )
                
                return {
                    "total_user_balance": data["total_user_balance"],
                    "total_successful_withdrawals": data["total_successful_withdrawals"],
                    "total_pending_withdrawals": data["total_pending_withdrawals"],
                    "total_joining_bonuses": data["total_joining_bonuses"],
                    "total_distributed": total_distributed,
                    "users_with_withdrawals": data["users_with_withdrawals"],
                    "users_with_pending": data["users_with_pending"]
                }
            
            return {
                "total_user_balance": 0,
                "total_successful_withdrawals": 0,
                "total_pending_withdrawals": 0,
                "total_joining_bonuses": 0,
                "total_distributed": 0,
                "users_with_withdrawals": 0,
                "users_with_pending": 0
            }
            
        except Exception as e:
            logger.error(f"Error getting financial overview: {e}")
            return {}
    
    async def initialize_all_admin_settings(self) -> bool:
        """Initialize admin settings for all configured admins"""
        try:
            admin_ids = get_all_admin_ids()

            for admin_id in admin_ids:
                await self.get_admin_settings(admin_id)

            # Also create settings for admin_id = 0 (for gift broadcasts and level rewards)
            await self.get_admin_settings(0)

            # Initialize level rewards system
            await self.initialize_level_rewards_system()

            logger.info(f"Initialized admin settings for {len(admin_ids) + 1} admins")
            return True

        except Exception as e:
            logger.error(f"Error initializing admin settings: {e}")
            return False

    async def initialize_level_rewards_system(self) -> bool:
        """Initialize level rewards system with default configuration"""
        try:
            from services.level_rewards_service import LevelRewardsService
            from models.level_rewards import LevelRewardsModel

            level_rewards_service = LevelRewardsService()

            # Check if level rewards config already exists
            global_admin_id = 0
            collection = await get_collection(COLLECTIONS['admin_settings'])
            admin_settings = await collection.find_one({"admin_id": global_admin_id})

            if not admin_settings or 'level_rewards_config' not in admin_settings:
                # Create default configuration
                default_config = LevelRewardsModel.get_default_config()

                await collection.update_one(
                    {"admin_id": global_admin_id},
                    {
                        "$set": {
                            "level_rewards_config": default_config,
                            "level_rewards_enabled": True,
                            "updated_at": get_current_timestamp()
                        }
                    },
                    upsert=True
                )

                logger.info("Initialized default level rewards configuration")

            return True

        except Exception as e:
            logger.error(f"Error initializing level rewards system: {e}")
            return False
    
    async def get_withdrawal_settings(self, admin_id: int = None) -> Dict[str, Any]:
        """Get withdrawal settings"""
        try:
            admin_settings = await self.get_admin_settings(admin_id)
            return admin_settings.get('withdrawal_settings', {
                "enabled": True,
                "tax_type": "none",
                "tax_amount": 0,
                "tax_percentage": 0
            })
            
        except Exception as e:
            logger.error(f"Error getting withdrawal settings: {e}")
            return {
                "enabled": True,
                "tax_type": "none", 
                "tax_amount": 0,
                "tax_percentage": 0
            }
    
    async def update_withdrawal_settings(self, settings_data: Dict[str, Any], admin_id: int = None) -> bool:
        """Update withdrawal settings"""
        try:
            return await self.update_admin_setting('withdrawal_settings', settings_data, admin_id)

        except Exception as e:
            logger.error(f"Error updating withdrawal settings: {e}")
            return False

    # Duplicate method removed - using the first update_admin_setting method above

    async def toggle_maintenance_mode(self, admin_id: int) -> Dict[str, Any]:
        """Toggle maintenance mode (matching PHP logic exactly)"""
        try:
            # Get current status
            admin_settings = await self.get_admin_settings(admin_id)
            current_status = admin_settings.get('maintenance_status', 'Off')

            # Toggle status
            new_status = 'Off' if current_status == 'On' else 'On'

            # Update setting
            success = await self.update_admin_setting('maintenance_status', new_status, admin_id)

            if success:
                status_text = "enabled" if new_status == 'On' else "disabled"
                return {
                    'success': True,
                    'new_status': new_status,
                    'message': f"Maintenance mode {status_text} successfully."
                }
            else:
                return {
                    'success': False,
                    'new_status': current_status,
                    'message': 'Error toggling maintenance mode.'
                }

        except Exception as e:
            logger.error(f"Error toggling maintenance mode: {e}")
            return {
                'success': False,
                'new_status': 'Off',
                'message': 'Error toggling maintenance mode.'
            }

    async def calculate_withdrawal_tax(self, amount: float, admin_id: int = None) -> Dict[str, Any]:
        """Calculate withdrawal tax with complete information"""
        try:
            withdrawal_settings = await self.get_withdrawal_settings(admin_id)

            tax_type = withdrawal_settings.get('tax_type', 'none')
            tax_amount = withdrawal_settings.get('tax_amount', 0)
            tax_percentage = withdrawal_settings.get('tax_percentage', 0)

            if tax_type == 'none':
                return {
                    'original_amount': amount,
                    'tax_deducted': 0,
                    'final_amount': amount,
                    'tax_type': 'none',
                    'tax_percentage': 0,
                    'tax_amount': 0
                }
            elif tax_type == 'fixed':
                tax = min(tax_amount, amount)  # Tax cannot exceed withdrawal amount
                return {
                    'original_amount': amount,
                    'tax_deducted': tax,
                    'final_amount': amount - tax,
                    'tax_type': 'fixed',
                    'tax_percentage': 0,
                    'tax_amount': tax_amount
                }
            elif tax_type == 'percentage':
                tax = (amount * tax_percentage) / 100
                return {
                    'original_amount': amount,
                    'tax_deducted': tax,
                    'final_amount': amount - tax,
                    'tax_type': 'percentage',
                    'tax_percentage': tax_percentage,
                    'tax_amount': 0
                }

            return {
                'original_amount': amount,
                'tax_deducted': 0,
                'final_amount': amount,
                'tax_type': 'none',
                'tax_percentage': 0,
                'tax_amount': 0
            }

        except Exception as e:
            logger.error(f"Error calculating withdrawal tax: {e}")
            return {
                'original_amount': amount,
                'tax_deducted': 0,
                'final_amount': amount,
                'tax_type': 'none',
                'tax_percentage': 0,
                'tax_amount': 0
            }

    # Broadcasting methods
    async def start_message_broadcast(
        self,
        admin_id: int,
        message_data: Dict[str, Any],
        target_users: Optional[List[int]] = None
    ) -> Dict[str, Any]:
        """Start message broadcast (matching PHP logic exactly)"""
        try:
            # Check if admin already has active broadcast
            active_broadcast = await self.get_active_broadcast(admin_id)
            if active_broadcast:
                return {
                    "success": False,
                    "error": f"You have an active broadcast (ID: {active_broadcast['broadcast_id'][-8:]}). Please wait for it to complete or send /cancel to stop it."
                }

            # Get target users
            if target_users is None:
                from services.user_service import UserService
                user_service = UserService()
                all_users = await user_service.get_all_users()
                target_users = [user['user_id'] for user in all_users if not user.get('banned', False)]

            if not target_users:
                return {"success": False, "error": "No users found to broadcast to"}

            # Create broadcast session
            from models.broadcast import BroadcastModel
            broadcast_session = BroadcastModel.create_broadcast_session(
                admin_id=admin_id,
                broadcast_type="message",
                message_data=message_data,
                target_users=target_users
            )

            # Save broadcast session
            broadcast_id = await self.save_broadcast_session(broadcast_session)
            if not broadcast_id:
                return {"success": False, "error": "Failed to create broadcast session"}

            # Start broadcast in background
            import asyncio
            asyncio.create_task(self._execute_message_broadcast(broadcast_id))

            return {
                "success": True,
                "broadcast_id": broadcast_id,
                "total_users": len(target_users),
                "message": f"📊 Broadcast started! ID: {broadcast_id[-8:]}\n\nBroadcasting to {len(target_users)} users..."
            }

        except Exception as e:
            logger.error(f"Error starting message broadcast: {e}")
            return {"success": False, "error": "Internal error occurred"}

    async def start_gift_broadcast(
        self,
        admin_id: int,
        channel: str,
        amount: float,
        channel_data: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Start gift broadcast (matching PHP logic exactly)"""
        try:
            # Check if admin already has active broadcast
            active_broadcast = await self.get_active_broadcast(admin_id)
            if active_broadcast:
                return {
                    "success": False,
                    "error": f"You have an active broadcast (ID: {active_broadcast['broadcast_id'][-8:]}). Please wait for it to complete or send /cancel to stop it."
                }

            # Get all users
            from services.user_service import UserService
            user_service = UserService()
            all_users = await user_service.get_all_users()
            target_users = [user['user_id'] for user in all_users if not user.get('banned', False)]

            if not target_users:
                return {"success": False, "error": "No users found to broadcast to"}

            # Store current gift broadcast data
            from models.broadcast import GiftBroadcastModel
            gift_broadcast_data = GiftBroadcastModel.create_gift_broadcast_data(
                channel, amount, channel_data
            )

            if not await self.set_current_gift_broadcast(gift_broadcast_data):
                return {"success": False, "error": "Failed to store gift broadcast data"}

            # Generate gift message and keyboard
            join_link, join_text = self._generate_join_link_and_text(channel_data)
            gift_message = GiftBroadcastModel.format_gift_message(
                channel_data.get("title", channel), amount, join_link, join_text
            )
            gift_keyboard = GiftBroadcastModel.create_gift_keyboard(join_link, join_text)

            # Create message data for broadcast
            message_data = {
                "type": "gift",
                "text": gift_message,
                "reply_markup": gift_keyboard.to_dict(),
                "gift_data": gift_broadcast_data
            }

            # Create broadcast session
            from models.broadcast import BroadcastModel
            broadcast_session = BroadcastModel.create_broadcast_session(
                admin_id=admin_id,
                broadcast_type="gift",
                message_data=message_data,
                target_users=target_users
            )

            # Save broadcast session
            broadcast_id = await self.save_broadcast_session(broadcast_session)
            if not broadcast_id:
                return {"success": False, "error": "Failed to create broadcast session"}

            # Start broadcast in background
            import asyncio
            asyncio.create_task(self._execute_gift_broadcast(broadcast_id))

            return {
                "success": True,
                "broadcast_id": broadcast_id,
                "total_users": len(target_users),
                "message": f"🎁 Gift broadcast started! ID: {broadcast_id[-8:]}\n\nBroadcasting to {len(target_users)} users..."
            }

        except Exception as e:
            logger.error(f"Error starting gift broadcast: {e}")
            return {"success": False, "error": "Internal error occurred"}

    async def get_active_broadcast(self, admin_id: int) -> Optional[Dict[str, Any]]:
        """Get active broadcast for admin"""
        try:
            collection = await get_collection(COLLECTIONS['broadcast_sessions'])

            broadcast = await collection.find_one({
                "admin_id": admin_id,
                "status": "active",
                "cancelled": False
            })

            return broadcast

        except Exception as e:
            logger.error(f"Error getting active broadcast: {e}")
            return None

    async def save_broadcast_session(self, broadcast_session: Dict[str, Any]) -> Optional[str]:
        """Save broadcast session to database"""
        try:
            collection = await get_collection(COLLECTIONS['broadcast_sessions'])

            result = await collection.insert_one(broadcast_session)

            if result.inserted_id:
                return broadcast_session["broadcast_id"]

            return None

        except Exception as e:
            logger.error(f"Error saving broadcast session: {e}")
            return None

    async def set_current_gift_broadcast(self, gift_data: Dict[str, Any]) -> bool:
        """Set current gift broadcast data"""
        try:
            collection = await get_collection(COLLECTIONS['gift_broadcasts'])

            # Remove any existing gift broadcast
            await collection.delete_many({})

            # Insert new gift broadcast
            result = await collection.insert_one(gift_data)

            return result.inserted_id is not None

        except Exception as e:
            logger.error(f"Error setting current gift broadcast: {e}")
            return False

    async def get_current_gift_broadcast(self) -> Optional[Dict[str, Any]]:
        """Get current gift broadcast data"""
        try:
            collection = await get_collection(COLLECTIONS['gift_broadcasts'])

            gift_broadcast = await collection.find_one({})

            return gift_broadcast

        except Exception as e:
            logger.error(f"Error getting current gift broadcast: {e}")
            return None

    def _generate_join_link_and_text(self, channel_data: Dict[str, Any]) -> tuple:
        """Generate join link and text based on channel type"""
        if channel_data.get("type") == "private":
            join_link = channel_data.get("invite_link", "https://t.me/")
            join_text = "Click & Join Private Channel"
        else:
            username = channel_data.get("username", "")
            if username:
                join_link = f"https://t.me/{username}"
            else:
                join_link = channel_data.get("invite_link", "https://t.me/")
            join_text = "Click & Join Channel"

        return join_link, join_text

    async def _execute_message_broadcast(self, broadcast_id: str):
        """Execute message broadcast in background"""
        try:
            session = await self.get_broadcast_session(broadcast_id)
            if not session:
                logger.error(f"Broadcast session {broadcast_id} not found")
                return

            admin_id = session["admin_id"]
            message_data = session["message_data"]
            target_users = session["target_users"]

            logger.info(f"Starting message broadcast {broadcast_id} to {len(target_users)} users")

            # Send initial progress message to admin and store message ID
            progress_message_id = await self._send_initial_progress_message(admin_id, broadcast_id, len(target_users))

            # Execute optimized broadcast
            await self._process_broadcast_users_optimized(broadcast_id, message_data, target_users, admin_id, progress_message_id)

        except Exception as e:
            logger.error(f"Error executing message broadcast {broadcast_id}: {e}")

    async def _execute_gift_broadcast(self, broadcast_id: str):
        """Execute gift broadcast in background"""
        try:
            session = await self.get_broadcast_session(broadcast_id)
            if not session:
                logger.error(f"Broadcast session {broadcast_id} not found")
                return

            admin_id = session["admin_id"]
            message_data = session["message_data"]
            target_users = session["target_users"]

            logger.info(f"Starting gift broadcast {broadcast_id} to {len(target_users)} users")

            # Send initial progress message to admin and store message ID
            progress_message_id = await self._send_initial_progress_message(admin_id, broadcast_id, len(target_users))

            # Execute optimized broadcast
            await self._process_broadcast_users_optimized(broadcast_id, message_data, target_users, admin_id, progress_message_id)

        except Exception as e:
            logger.error(f"Error executing gift broadcast {broadcast_id}: {e}")

    async def get_broadcast_session(self, broadcast_id: str) -> Optional[Dict[str, Any]]:
        """Get broadcast session by ID"""
        try:
            collection = await get_collection(COLLECTIONS['broadcast_sessions'])

            session = await collection.find_one({"broadcast_id": broadcast_id})

            return session

        except Exception as e:
            logger.error(f"Error getting broadcast session: {e}")
            return None

    async def _process_broadcast_users_optimized(
        self,
        broadcast_id: str,
        message_data: Dict[str, Any],
        target_users: List[int],
        admin_id: int,
        progress_message_id: int = None
    ):
        """Process broadcast to all target users with optimized performance"""
        try:
            from telegram import Bot
            bot = Bot(settings.BOT_TOKEN)

            session = await self.get_broadcast_session(broadcast_id)
            if not session:
                return

            # Use optimized broadcast service
            from services.broadcast_service import BroadcastService
            broadcast_service = BroadcastService()
            broadcast_service.set_bot(bot)

            # Create progress callback for real-time updates
            async def progress_callback(stats, processed_count, total_users, start_time):
                if progress_message_id:
                    await self._update_progress_message(
                        admin_id, progress_message_id, stats, processed_count, total_users, start_time
                    )

            # Create broadcast data structure
            broadcast_data = {
                "broadcast_id": broadcast_id,
                "admin_id": admin_id,
                "status": "in_progress",
                "broadcast_data": message_data,
                "total_users": len(target_users),
                "successful_sends": 0,
                "failed_sends": 0,
                "blocked_users": 0,
                "started_at": get_current_timestamp(),
                "completed_at": None
            }

            # Save broadcast log
            collection = await get_collection(COLLECTIONS['broadcast_logs'])
            await collection.insert_one(broadcast_data)

            # Execute optimized broadcast
            result = await broadcast_service.execute_broadcast_optimized(
                broadcast_id, admin_id, progress_callback
            )

            # Send final completion message
            if result["success"]:
                await self._send_broadcast_completion_message(admin_id, broadcast_id, result["statistics"])
            else:
                await self._send_broadcast_error_message(admin_id, broadcast_id, result["error"])

        except Exception as e:
            logger.error(f"Error in optimized broadcast processing: {e}")
            await self._send_broadcast_error_message(admin_id, broadcast_id, str(e))

    async def _update_progress_message(self, admin_id: int, message_id: int, stats: Dict, processed: int, total: int, start_time: str):
        """Update progress message with real-time statistics"""
        try:
            from telegram import Bot
            bot = Bot(settings.BOT_TOKEN)

            # Calculate progress percentage
            progress_percent = round((processed / total) * 100, 1) if total > 0 else 0

            # Calculate estimated time remaining
            elapsed_time = get_current_timestamp() - start_time
            if processed > 0:
                avg_time_per_user = elapsed_time / processed
                remaining_users = total - processed
                eta_seconds = avg_time_per_user * remaining_users
                eta_minutes = int(eta_seconds / 60)
                eta_text = f"{eta_minutes}m {int(eta_seconds % 60)}s" if eta_minutes > 0 else f"{int(eta_seconds)}s"
            else:
                eta_text = "Calculating..."

            # Create progress message
            progress_message = f"📊 <b>Broadcast Progress</b>\n\n"
            progress_message += f"⏳ <b>Progress:</b> {processed}/{total} ({progress_percent}%)\n"
            progress_message += f"✅ <b>Sent:</b> {stats['successful_sends']}\n"
            progress_message += f"❌ <b>Failed:</b> {stats['failed_sends']}\n"
            progress_message += f"🚫 <b>Blocked:</b> {stats['blocked_users']}\n"
            progress_message += f"💀 <b>Deactivated:</b> {stats['deactivated_users']}\n"
            progress_message += f"🔍 <b>Not Found:</b> {stats['chat_not_found']}\n"
            progress_message += f"⚡ <b>Rate Limited:</b> {stats['rate_limited']}\n"
            progress_message += f"🧹 <b>Cleaned:</b> {len(stats['cleaned_users'])}\n\n"
            progress_message += f"⏱️ <b>ETA:</b> {eta_text}"

            await bot.edit_message_text(
                chat_id=admin_id,
                message_id=message_id,
                text=progress_message,
                parse_mode='HTML'
            )

        except Exception as e:
            logger.error(f"Error updating progress message: {e}")

    async def _send_broadcast_completion_message(self, admin_id: int, broadcast_id: str, stats: Dict):
        """Send broadcast completion message with detailed statistics"""
        try:
            from telegram import Bot
            bot = Bot(settings.BOT_TOKEN)

            # Calculate success rate
            total_attempted = stats["successful_sends"] + stats["failed_sends"] + stats["blocked_users"] + stats["deactivated_users"] + stats["chat_not_found"] + stats["other_errors"]
            success_rate = round((stats["successful_sends"] / total_attempted) * 100, 2) if total_attempted > 0 else 0

            completion_message = f"✅ <b>Broadcast Completed Successfully!</b>\n\n"
            completion_message += f"🆔 <b>Broadcast ID:</b> {broadcast_id[-8:]}\n"
            completion_message += f"👥 <b>Total Users:</b> {stats['total_users']:,}\n\n"
            completion_message += f"📊 <b>Detailed Statistics:</b>\n"
            completion_message += f"✅ Successfully Sent: {stats['successful_sends']:,}\n"
            completion_message += f"❌ Failed Sends: {stats['failed_sends']:,}\n"
            completion_message += f"🚫 Blocked Users: {stats['blocked_users']:,}\n"
            completion_message += f"💀 Deactivated: {stats['deactivated_users']:,}\n"
            completion_message += f"🔍 Chat Not Found: {stats['chat_not_found']:,}\n"
            completion_message += f"⚡ Rate Limited: {stats['rate_limited']:,}\n"
            completion_message += f"🔧 Other Errors: {stats['other_errors']:,}\n\n"
            completion_message += f"🧹 <b>Database Cleanup:</b>\n"
            completion_message += f"Cleaned {len(stats['cleaned_users']):,} inactive users\n\n"
            completion_message += f"📈 <b>Success Rate:</b> {success_rate}%\n"
            completion_message += f"🎉 <b>Broadcast completed successfully!</b>"

            await bot.send_message(
                chat_id=admin_id,
                text=completion_message,
                parse_mode='HTML'
            )

        except Exception as e:
            logger.error(f"Error sending completion message: {e}")

    async def _send_broadcast_error_message(self, admin_id: int, broadcast_id: str, error: str):
        """Send broadcast error message"""
        try:
            from telegram import Bot
            bot = Bot(settings.BOT_TOKEN)

            error_message = f"❌ <b>Broadcast Failed</b>\n\n"
            error_message += f"🆔 <b>Broadcast ID:</b> {broadcast_id[-8:]}\n"
            error_message += f"🚨 <b>Error:</b> {error}\n\n"
            error_message += f"Please try again or contact support if the issue persists."

            await bot.send_message(
                chat_id=admin_id,
                text=error_message,
                parse_mode='HTML'
            )

        except Exception as e:
            logger.error(f"Error sending error message: {e}")

            for user_id in target_users:
                # Check if broadcast was cancelled
                session = await self.get_broadcast_session(broadcast_id)
                if not session or session.get("cancelled", False):
                    logger.info(f"Broadcast {broadcast_id} cancelled at user {processed_count}/{total_users}")

                    # Edit progress message to show cancellation status
                    if progress_message_id:
                        from models.broadcast import BroadcastModel
                        from utils.helpers import edit_safe_message

                        cancellation_progress = f"🚫 <b>Broadcast Cancelled</b>\n\n"
                        cancellation_progress += f"🆔 <b>ID:</b> <code>{broadcast_id[-8:]}</code>\n"
                        cancellation_progress += f"📊 <b>Final Progress:</b> {processed_count}/{total_users} ({(processed_count/total_users)*100:.1f}%)\n"
                        cancellation_progress += f"✅ <b>Sent:</b> {sent_count}\n"
                        cancellation_progress += f"❌ <b>Failed:</b> {failed_count}\n"
                        cancellation_progress += f"🚫 <b>Blocked:</b> {blocked_count}\n\n"
                        cancellation_progress += "🛑 <b>Status:</b> Stopped by admin"

                        await edit_safe_message(bot, admin_id, progress_message_id, cancellation_progress, parse_mode='HTML')

                    # Send separate completion notification
                    completion_message = f"✅ <b>Broadcast Cancellation Complete</b>\n\n"
                    completion_message += f"The broadcast has been stopped successfully.\n"
                    completion_message += f"Final stats: {sent_count} sent, {failed_count} failed, {blocked_count} blocked."

                    await send_safe_message(bot, admin_id, completion_message, parse_mode='HTML')

                    # Clean up cancelled broadcast session
                    await self.cleanup_broadcast_session(broadcast_id)
                    break

                processed_count += 1

                try:
                    # Send message based on type
                    success = await self._send_broadcast_message(bot, user_id, message_data)

                    if success:
                        sent_count += 1
                    else:
                        failed_count += 1

                except Exception as e:
                    error_msg = str(e)
                    if "blocked" in error_msg.lower() or "deactivated" in error_msg.lower():
                        blocked_count += 1
                    else:
                        failed_count += 1

                    logger.error(f"Error sending to user {user_id}: {e}")

                # Update progress every 50 users or every 10 seconds
                current_time = get_current_timestamp()
                if (processed_count % 50 == 0) or (current_time - last_progress_update >= 10):
                    percent = (processed_count / total_users) * 100
                    remaining_users = total_users - processed_count
                    elapsed_time = current_time - start_time

                    if processed_count > 0:
                        avg_time_per_user = elapsed_time / processed_count
                        eta_seconds = int(remaining_users * avg_time_per_user)
                    else:
                        eta_seconds = remaining_users

                    await self._edit_progress_update(
                        admin_id, broadcast_id, processed_count, total_users,
                        sent_count, failed_count, blocked_count, percent, eta_seconds,
                        progress_message_id
                    )

                    last_progress_update = current_time

                # Rate limiting
                import asyncio
                await asyncio.sleep(0.1)  # 100ms delay between messages

                # Additional delay every 10 messages
                if processed_count % 10 == 0:
                    await asyncio.sleep(0.2)  # 200ms additional delay

            # Update progress message with final status and send completion message
            end_time = get_current_timestamp()
            duration = end_time - start_time
            success_rate = (sent_count / total_users) * 100 if total_users > 0 else 0

            # Edit progress message to show completion
            if progress_message_id:
                from utils.helpers import edit_safe_message

                final_progress = f"✅ <b>Broadcast Completed</b>\n\n"
                final_progress += f"🆔 <b>ID:</b> <code>{broadcast_id[-8:]}</code>\n"
                final_progress += f"📊 <b>Final Results:</b> {total_users} users\n"
                final_progress += f"✅ <b>Sent:</b> {sent_count}\n"
                final_progress += f"❌ <b>Failed:</b> {failed_count}\n"
                final_progress += f"🚫 <b>Blocked:</b> {blocked_count}\n"
                final_progress += f"📈 <b>Success Rate:</b> {success_rate:.1f}%\n\n"
                final_progress += f"🎉 <b>Status:</b> Successfully completed!"

                await edit_safe_message(bot, admin_id, progress_message_id, final_progress, parse_mode='HTML')

            # Send separate detailed completion message
            from models.broadcast import BroadcastModel
            completion_message = BroadcastModel.format_broadcast_completion_message(
                broadcast_id, total_users, sent_count, failed_count, blocked_count,
                duration, success_rate, message_data.get("type", "message")
            )

            await send_safe_message(bot, admin_id, completion_message, parse_mode='HTML')

            # Update session status
            await self.update_broadcast_session(broadcast_id, {
                "status": "completed",
                "sent_count": sent_count,
                "failed_count": failed_count,
                "blocked_count": blocked_count,
                "end_time": end_time,
                "duration": duration,
                "success_rate": success_rate
            })

            # Clean up broadcast session after completion
            await self.cleanup_broadcast_session(broadcast_id)

            logger.info(f"Broadcast {broadcast_id} completed. Success: {sent_count}, Failed: {failed_count}, Blocked: {blocked_count}")

        except Exception as e:
            logger.error(f"Error processing broadcast users for {broadcast_id}: {e}")

    async def _send_broadcast_message(self, bot, user_id: int, message_data: Dict[str, Any]) -> bool:
        """Send broadcast message to user"""
        try:
            # Handle different broadcast data structures
            if message_data.get("media"):
                # New structure: media is stored separately
                media_data = message_data["media"]
                media_type = media_data["type"]
                file_id = media_data["file_id"]
                caption = message_data.get("text", "")

                if media_type == "photo":
                    await bot.send_photo(
                        chat_id=user_id,
                        photo=file_id,
                        caption=caption if caption else None,
                        parse_mode='HTML'
                    )
                elif media_type == "video":
                    await bot.send_video(
                        chat_id=user_id,
                        video=file_id,
                        caption=caption if caption else None,
                        parse_mode='HTML'
                    )
                elif media_type == "document":
                    await bot.send_document(
                        chat_id=user_id,
                        document=file_id,
                        caption=caption if caption else None,
                        parse_mode='HTML'
                    )
                elif media_type == "audio":
                    await bot.send_audio(
                        chat_id=user_id,
                        audio=file_id,
                        caption=caption if caption else None,
                        parse_mode='HTML'
                    )
                elif media_type == "voice":
                    await bot.send_voice(
                        chat_id=user_id,
                        voice=file_id,
                        caption=caption if caption else None,
                        parse_mode='HTML'
                    )
                elif media_type == "sticker":
                    await bot.send_sticker(
                        chat_id=user_id,
                        sticker=file_id
                    )
                elif media_type == "animation":
                    await bot.send_animation(
                        chat_id=user_id,
                        animation=file_id,
                        caption=caption if caption else None,
                        parse_mode='HTML'
                    )
                elif media_type == "video_note":
                    await bot.send_video_note(
                        chat_id=user_id,
                        video_note=file_id
                    )
                return True

            elif message_data.get("type") == "text" or message_data.get("text"):
                # Text message
                text = message_data.get("text", "")
                return await send_safe_message(
                    bot, user_id, text, parse_mode='HTML'
                )

            elif message_data.get("type") == "gift":
                # Gift broadcast
                from telegram import InlineKeyboardMarkup
                keyboard = InlineKeyboardMarkup.de_json(message_data["reply_markup"], bot)

                return await send_safe_message(
                    bot, user_id, message_data["text"],
                    reply_markup=keyboard, parse_mode='HTML'
                )

            # Legacy structure support (for backward compatibility)
            elif message_data.get("photo"):
                await bot.send_photo(
                    chat_id=user_id,
                    photo=message_data["photo"],
                    caption=message_data.get("caption", ""),
                    parse_mode='HTML'
                )
                return True

            elif message_data.get("video"):
                await bot.send_video(
                    chat_id=user_id,
                    video=message_data["video"],
                    caption=message_data.get("caption", ""),
                    parse_mode='HTML'
                )
                return True

            elif message_data.get("document"):
                await bot.send_document(
                    chat_id=user_id,
                    document=message_data["document"],
                    caption=message_data.get("caption", ""),
                    parse_mode='HTML'
                )
                return True

            return False

        except Exception as e:
            logger.error(f"Error sending broadcast message to {user_id}: {e}")
            return False

    async def _send_progress_update(
        self,
        admin_id: int,
        broadcast_id: str,
        processed: int,
        total: int,
        success: int,
        failed: int,
        blocked: int,
        percent: float,
        eta_seconds: int
    ):
        """Send progress update to admin"""
        try:
            from telegram import Bot
            from models.broadcast import BroadcastModel

            bot = Bot(settings.BOT_TOKEN)

            progress_message = BroadcastModel.format_broadcast_progress_message(
                broadcast_id, processed, total, success, failed, blocked, percent, eta_seconds
            )

            await send_safe_message(bot, admin_id, progress_message, parse_mode='HTML')

        except Exception as e:
            logger.error(f"Error sending progress update: {e}")

    async def _send_initial_progress_message(
        self,
        admin_id: int,
        broadcast_id: str,
        total_users: int
    ) -> int:
        """Send initial progress message and return message ID"""
        try:
            from telegram import Bot
            from models.broadcast import BroadcastModel

            bot = Bot(settings.BOT_TOKEN)

            # Create initial progress message
            progress_message = BroadcastModel.format_broadcast_progress_message(
                broadcast_id, 0, total_users, 0, 0, 0, 0.0, total_users
            )

            # Send message and get the message object
            message = await bot.send_message(
                chat_id=admin_id,
                text=progress_message,
                parse_mode='HTML'
            )

            # Return the message ID for future edits
            return message.message_id

        except Exception as e:
            logger.error(f"Error sending initial progress message: {e}")
            return None

    async def _edit_progress_update(
        self,
        admin_id: int,
        broadcast_id: str,
        processed: int,
        total: int,
        success: int,
        failed: int,
        blocked: int,
        percent: float,
        eta_seconds: int,
        message_id: int
    ):
        """Edit existing progress message instead of sending new one"""
        try:
            if not message_id:
                # Fallback to sending new message if no message ID
                await self._send_progress_update(
                    admin_id, broadcast_id, processed, total,
                    success, failed, blocked, percent, eta_seconds
                )
                return

            from telegram import Bot
            from models.broadcast import BroadcastModel
            from utils.helpers import edit_safe_message

            bot = Bot(settings.BOT_TOKEN)

            progress_message = BroadcastModel.format_broadcast_progress_message(
                broadcast_id, processed, total, success, failed, blocked, percent, eta_seconds
            )

            # Edit the existing message
            success = await edit_safe_message(
                bot, admin_id, message_id, progress_message, parse_mode='HTML'
            )

            if not success:
                logger.warning(f"Failed to edit progress message {message_id}, falling back to new message")
                # Fallback to sending new message if edit fails
                await self._send_progress_update(
                    admin_id, broadcast_id, processed, total,
                    success, failed, blocked, percent, eta_seconds
                )

        except Exception as e:
            logger.error(f"Error editing progress update: {e}")
            # Fallback to sending new message on error
            await self._send_progress_update(
                admin_id, broadcast_id, processed, total,
                success, failed, blocked, percent, eta_seconds
            )

    async def update_broadcast_session(self, broadcast_id: str, update_data: Dict[str, Any]) -> bool:
        """Update broadcast session"""
        try:
            collection = await get_collection(COLLECTIONS['broadcast_sessions'])

            update_data["updated_at"] = get_current_timestamp()

            result = await collection.update_one(
                {"broadcast_id": broadcast_id},
                {"$set": update_data}
            )

            return result.modified_count > 0

        except Exception as e:
            logger.error(f"Error updating broadcast session: {e}")
            return False

    async def cancel_broadcast(self, broadcast_id: str, admin_id: int) -> bool:
        """Cancel active broadcast"""
        try:
            collection = await get_collection(COLLECTIONS['broadcast_sessions'])

            # Update broadcast session to cancelled
            update_data = {
                "cancelled": True,
                "cancelled_at": get_current_timestamp(),
                "cancelled_by": admin_id,
                "status": "cancelled",
                "updated_at": get_current_timestamp()
            }

            result = await collection.update_one(
                {"broadcast_id": broadcast_id, "admin_id": admin_id},
                {"$set": update_data}
            )

            if result.modified_count > 0:
                logger.info(f"Admin {admin_id} cancelled broadcast {broadcast_id}")
                return True

            return False

        except Exception as e:
            logger.error(f"Error cancelling broadcast: {e}")
            return False

    async def cleanup_broadcast_session(self, broadcast_id: str) -> bool:
        """Clean up completed broadcast session"""
        try:
            collection = await get_collection(COLLECTIONS['broadcast_sessions'])

            result = await collection.delete_one({"broadcast_id": broadcast_id})

            if result.deleted_count > 0:
                logger.info(f"Cleaned up broadcast session {broadcast_id}")
                return True

            return False

        except Exception as e:
            logger.error(f"Error cleaning up broadcast session: {e}")
            return False

    async def cleanup_stale_broadcast_sessions(self, max_age_hours: int = 24) -> int:
        """Clean up stale broadcast sessions older than max_age_hours"""
        try:
            collection = await get_collection(COLLECTIONS['broadcast_sessions'])

            # Calculate cutoff time
            cutoff_time = get_current_timestamp() - (max_age_hours * 3600)

            # Find and delete stale sessions
            result = await collection.delete_many({
                "created_at": {"$lt": cutoff_time}
            })

            if result.deleted_count > 0:
                logger.info(f"Cleaned up {result.deleted_count} stale broadcast sessions")

            return result.deleted_count

        except Exception as e:
            logger.error(f"Error cleaning up stale broadcast sessions: {e}")
            return 0

    async def get_all_active_broadcasts(self) -> List[Dict[str, Any]]:
        """Get all active broadcast sessions"""
        try:
            collection = await get_collection(COLLECTIONS['broadcast_sessions'])

            cursor = collection.find({
                "status": "active",
                "cancelled": False
            })

            broadcasts = await cursor.to_list(length=None)
            return broadcasts

        except Exception as e:
            logger.error(f"Error getting active broadcasts: {e}")
            return []
