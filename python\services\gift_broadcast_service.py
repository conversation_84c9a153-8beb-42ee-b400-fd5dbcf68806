"""
Gift Broadcast service for channel-based reward campaigns
Handles channel validation, membership verification, and reward distribution
"""

import logging
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime
from telegram import Bo<PERSON>
from telegram.error import TelegramError

from config.database import get_collection, COLLECTIONS
from utils.helpers import get_current_timestamp, generate_unique_id
from config.settings import settings

logger = logging.getLogger(__name__)

class GiftBroadcastService:
    """Service for gift broadcast campaigns with channel join verification"""

    def __init__(self, bot: Bot = None):
        if bot:
            self.bot = bot
            self._bot_initialized = True
        else:
            # Create bot instance if not provided (for backward compatibility)
            self.bot = Bot(token=settings.BOT_TOKEN)
            self._bot_initialized = False

    async def _ensure_bot_initialized(self):
        """Ensure bot instance is properly initialized"""
        if not self._bot_initialized:
            try:
                # Initialize the bot by calling get_me
                await self.bot.initialize()
                self._bot_initialized = True
            except Exception as e:
                logger.error(f"Failed to initialize bot: {e}")
                raise RuntimeError("Bot initialization failed")
    
    async def validate_channel_input(self, channel_input: str) -> Dict[str, Any]:
        """Validate and parse channel input (username or ID)"""
        try:
            channel_input = channel_input.strip()
            
            # Check if it's a channel ID (starts with -100)
            if channel_input.startswith('-100') and channel_input[4:].isdigit():
                # Private channel ID
                return {
                    'type': 'private',
                    'channel_id': channel_input,
                    'needs_invite_link': True,
                    'valid': True
                }
            
            # Check if it's a public channel username
            elif channel_input.replace('@', '').isalnum():
                # Remove @ if present and validate
                username = channel_input.replace('@', '')
                channel_id = f"@{username}"
                
                return {
                    'type': 'public',
                    'channel_id': channel_id,
                    'username': username,
                    'needs_invite_link': False,
                    'valid': True
                }
            
            else:
                return {
                    'valid': False,
                    'error': 'Invalid format. Use channel username (without @) or channel ID (-100xxxxxxxxx)'
                }
                
        except Exception as e:
            logger.error(f"Error validating channel input: {e}")
            return {
                'valid': False,
                'error': 'Failed to validate channel input'
            }
    
    async def verify_channel_access(self, channel_id: str) -> Tuple[bool, str, Optional[Dict]]:
        """Verify bot has admin access to channel and get channel info"""
        try:
            # Ensure bot is initialized
            await self._ensure_bot_initialized()

            # Get channel information
            chat = await self.bot.get_chat(channel_id)
            
            # Check if bot is admin
            chat_member = await self.bot.get_chat_member(channel_id, self.bot.id)
            
            if chat_member.status not in ['administrator', 'creator']:
                return False, "Bot must be an administrator in the channel", None
            
            # Return channel info
            channel_info = {
                'id': str(chat.id),
                'title': chat.title,
                'username': chat.username,
                'type': chat.type,
                'member_count': getattr(chat, 'member_count', 0)
            }
            
            return True, "Channel access verified", channel_info
            
        except TelegramError as e:
            if "chat not found" in str(e).lower():
                return False, "Channel not found. Please check the channel ID/username", None
            elif "forbidden" in str(e).lower():
                return False, "Bot doesn't have access to this channel", None
            else:
                return False, f"Telegram error: {str(e)}", None
        except Exception as e:
            logger.error(f"Error verifying channel access: {e}")
            return False, "Failed to verify channel access", None
    
    async def create_gift_broadcast(
        self, 
        channel_info: Dict[str, Any], 
        invite_link: Optional[str], 
        reward_amount: float,
        admin_user_id: int
    ) -> Tuple[bool, str, Optional[str]]:
        """Create a gift broadcast campaign"""
        try:
            # Generate unique broadcast ID
            broadcast_id = generate_unique_id("gift_broadcast")
            
            # Create gift broadcast record
            gift_broadcasts_collection = await get_collection(COLLECTIONS['gift_broadcasts'])
            
            gift_broadcast_data = {
                'broadcast_id': broadcast_id,
                'channel_id': channel_info['id'],
                'channel_title': channel_info['title'],
                'channel_username': channel_info.get('username'),
                'channel_type': channel_info['type'],
                'invite_link': invite_link,
                'reward_amount': reward_amount,
                'created_by': admin_user_id,
                'created_at': get_current_timestamp(),
                'status': 'active',
                'total_participants': 0,
                'total_rewards_distributed': 0,
                'participants': []
            }
            
            result = await gift_broadcasts_collection.insert_one(gift_broadcast_data)
            
            if result.inserted_id:
                return True, "Gift broadcast created successfully", broadcast_id
            else:
                return False, "Failed to create gift broadcast", None
                
        except Exception as e:
            logger.error(f"Error creating gift broadcast: {e}")
            return False, "Failed to create gift broadcast", None
    
    async def get_gift_broadcast(self, broadcast_id: str) -> Optional[Dict[str, Any]]:
        """Get gift broadcast by ID"""
        try:
            gift_broadcasts_collection = await get_collection(COLLECTIONS['gift_broadcasts'])
            return await gift_broadcasts_collection.find_one({'broadcast_id': broadcast_id})
        except Exception as e:
            logger.error(f"Error getting gift broadcast: {e}")
            return None
    
    async def check_user_participation(self, broadcast_id: str, user_id: int) -> bool:
        """Check if user has already participated in gift broadcast"""
        try:
            gift_broadcasts_collection = await get_collection(COLLECTIONS['gift_broadcasts'])
            broadcast = await gift_broadcasts_collection.find_one({
                'broadcast_id': broadcast_id,
                'participants': user_id
            })
            return broadcast is not None
        except Exception as e:
            logger.error(f"Error checking user participation: {e}")
            return False
    
    async def verify_channel_membership(self, channel_id: str, user_id: int) -> bool:
        """Verify if user is a member of the channel"""
        try:
            # Ensure bot is initialized
            await self._ensure_bot_initialized()

            chat_member = await self.bot.get_chat_member(channel_id, user_id)
            return chat_member.status in ['member', 'administrator', 'creator']
        except TelegramError:
            return False
        except Exception as e:
            logger.error(f"Error verifying channel membership: {e}")
            return False
    
    async def process_user_participation(self, broadcast_id: str, user_id: int) -> Tuple[bool, str, float, dict]:
        """Process user participation and distribute reward"""
        try:
            # Get gift broadcast
            gift_broadcast = await self.get_gift_broadcast(broadcast_id)
            if not gift_broadcast:
                return False, "Gift broadcast not found", 0, {}

            if gift_broadcast['status'] != 'active':
                return False, "Gift broadcast is no longer active", 0, {}

            # Check if user already participated
            if await self.check_user_participation(broadcast_id, user_id):
                return False, "You have already participated in this gift broadcast", 0, {}

            # Verify channel membership
            channel_id = gift_broadcast['channel_id']
            if not await self.verify_channel_membership(channel_id, user_id):
                # Return channel info for error message
                channel_info = {
                    'channel_title': gift_broadcast.get('channel_title', 'Channel'),
                    'invite_link': gift_broadcast.get('invite_link'),
                    'channel_username': gift_broadcast.get('channel_username'),
                    'broadcast_id': broadcast_id
                }
                return False, "You must join the channel first", 0, channel_info
            
            # Add user to participants and update statistics
            gift_broadcasts_collection = await get_collection(COLLECTIONS['gift_broadcasts'])
            reward_amount = gift_broadcast['reward_amount']
            
            await gift_broadcasts_collection.update_one(
                {'broadcast_id': broadcast_id},
                {
                    '$push': {'participants': user_id},
                    '$inc': {
                        'total_participants': 1,
                        'total_rewards_distributed': reward_amount
                    }
                }
            )
            
            # Credit user balance
            from services.user_service import UserService
            user_service = UserService()
            await user_service.update_balance(user_id, reward_amount, 'add')

            return True, f"Congratulations! You received ₹{reward_amount} for joining the channel!", reward_amount, {}

        except Exception as e:
            logger.error(f"Error processing user participation: {e}")
            return False, "Something went wrong while processing your participation", 0, {}
    
    async def get_broadcast_statistics(self, broadcast_id: str) -> Dict[str, Any]:
        """Get statistics for a gift broadcast"""
        try:
            gift_broadcast = await self.get_gift_broadcast(broadcast_id)
            if not gift_broadcast:
                return {}
            
            return {
                'broadcast_id': broadcast_id,
                'channel_title': gift_broadcast['channel_title'],
                'reward_amount': gift_broadcast['reward_amount'],
                'total_participants': gift_broadcast['total_participants'],
                'total_rewards_distributed': gift_broadcast['total_rewards_distributed'],
                'status': gift_broadcast['status'],
                'created_at': gift_broadcast['created_at']
            }
            
        except Exception as e:
            logger.error(f"Error getting broadcast statistics: {e}")
            return {}
    
    async def generate_join_message_and_keyboard(self, broadcast_id: str) -> Tuple[str, Any]:
        """Generate join message and keyboard for gift broadcast"""
        try:
            from telegram import InlineKeyboardButton, InlineKeyboardMarkup
            
            gift_broadcast = await self.get_gift_broadcast(broadcast_id)
            if not gift_broadcast:
                return "Gift broadcast not found", None
            
            channel_title = gift_broadcast['channel_title']
            reward_amount = gift_broadcast['reward_amount']
            invite_link = gift_broadcast.get('invite_link')
            channel_username = gift_broadcast.get('channel_username')
            
            # Build message
            message = f"🎁 <b>Gift Broadcast!</b>\n\n"
            message += f"Join <b>{channel_title}</b> and get ₹{reward_amount}!\n\n"
            message += f"💰 Reward: ₹{reward_amount}\n"
            message += f"📢 Channel: {channel_title}\n\n"
            message += f"Click the button below to join and claim your reward!"
            
            # Build keyboard
            keyboard = []
            
            # Join button
            if invite_link:
                keyboard.append([InlineKeyboardButton(f'📢 Join {channel_title}', url=invite_link)])
            elif channel_username:
                keyboard.append([InlineKeyboardButton(f'📢 Join {channel_title}', url=f'https://t.me/{channel_username}')])
            
            # Verify button
            keyboard.append([InlineKeyboardButton('✅ Verify & Claim Reward', callback_data=f'verify_gift_broadcast_{broadcast_id}')])
            
            return message, InlineKeyboardMarkup(keyboard)
            
        except Exception as e:
            logger.error(f"Error generating join message: {e}")
            return "Error generating message", None

    async def start_gift_broadcast_to_all_users(self, broadcast_data: Dict[str, Any]) -> Tuple[bool, str]:
        """Start gift broadcast to all bot users"""
        try:
            # Get all users
            from services.user_service import UserService
            user_service = UserService()
            all_users = await user_service.get_all_users()

            if not all_users:
                return False, "No users found to broadcast to"

            # Filter out banned users
            target_users = [user for user in all_users if not user.get('banned', False)]

            if not target_users:
                return False, "No active users found to broadcast to"

            # Generate broadcast message
            message = self._generate_gift_broadcast_message(broadcast_data)
            keyboard = self._generate_gift_broadcast_keyboard(broadcast_data)

            # Send to all users
            successful_sends = 0
            failed_sends = 0

            for user in target_users:
                try:
                    await self.bot.send_message(
                        chat_id=user['user_id'],
                        text=message,
                        reply_markup=keyboard,
                        parse_mode='HTML'
                    )
                    successful_sends += 1
                except Exception as e:
                    logger.warning(f"Failed to send gift broadcast to user {user['user_id']}: {e}")
                    failed_sends += 1

            # Update broadcast statistics
            await self._update_broadcast_stats(broadcast_data['broadcast_id'], successful_sends, failed_sends)

            return True, f"Broadcast sent to {successful_sends} users ({failed_sends} failed)"

        except Exception as e:
            logger.error(f"Error starting gift broadcast: {e}")
            return False, f"Error starting gift broadcast: {e}"

    def _generate_gift_broadcast_message(self, broadcast_data: Dict[str, Any]) -> str:
        """Generate the gift broadcast message"""
        channel_title = broadcast_data.get('channel_title', 'Channel')
        invite_link = broadcast_data.get('invite_link')
        channel_username = broadcast_data.get('channel_username')

        # Determine the channel link
        if invite_link:
            channel_link = invite_link
        elif channel_username:
            channel_link = f"https://t.me/{channel_username.replace('@', '')}"
        else:
            channel_link = "#"

        message = "🎁 <b>EXTRA BONUS</b> 👇!\n\n"
        message += f"👉 <a href=\"{channel_link}\"><b>Click & Join Channel</b></a>\n\n"
        message += "<blockquote>👆 <b>Must Join Above Channel</b> \n"
        message += "<b>Before Click [🎁 Claim Bonus]</b></blockquote>\n\n"
        message += "👇👇👇👇👇"

        return message

    def _generate_gift_broadcast_keyboard(self, broadcast_data: Dict[str, Any]):
        """Generate the gift broadcast keyboard"""
        from telegram import InlineKeyboardButton, InlineKeyboardMarkup

        broadcast_id = broadcast_data['broadcast_id']

        keyboard = InlineKeyboardMarkup([
            [InlineKeyboardButton('🎁 Claim Bonus', callback_data=f'verify_gift_broadcast_{broadcast_id}')]
        ])

        return keyboard

    async def _update_broadcast_stats(self, broadcast_id: str, successful_sends: int, failed_sends: int):
        """Update broadcast statistics"""
        try:
            collection = await get_collection(COLLECTIONS['gift_broadcasts'])
            await collection.update_one(
                {'broadcast_id': broadcast_id},
                {
                    '$set': {
                        'broadcast_sent': True,
                        'successful_sends': successful_sends,
                        'failed_sends': failed_sends,
                        'broadcast_sent_at': get_current_timestamp()
                    }
                }
            )
        except Exception as e:
            logger.error(f"Error updating broadcast stats: {e}")
