"""
Configuration settings for the Telegram Referral Bot
Maintains identical configuration structure to PHP version
"""

import os
from typing import List, Optional
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

class Settings:
    """Bot configuration settings"""
    
    # Telegram Bot Configuration
    BOT_TOKEN: str = os.getenv("BOT_TOKEN", "")
    BOT_USERNAME: str = os.getenv("BOT_USERNAME", "")
    
    # Admin Configuration
    ADMIN_IDS: List[int] = [
        int(admin_id.strip()) 
        for admin_id in os.getenv("ADMIN_IDS", "").split(",") 
        if admin_id.strip().isdigit()
    ]
    ADMIN_ID: int = ADMIN_IDS[0] if ADMIN_IDS else 0  # Primary admin for backward compatibility
    
    # Channel Configuration
    MAIN_CHANNEL: str = os.getenv("MAIN_CHANNEL", "")
    PRIVATE_LOGS_CHANNEL: str = os.getenv("PRIVATE_LOGS_CHANNEL", "")
    
    # MongoDB Configuration
    MONGODB_URI: str = os.getenv("MONGODB_URI", "")
    DATABASE_NAME: str = os.getenv("DATABASE_NAME", "referral_bot")
    
    # Bot Settings
    MAINTENANCE_MODE: bool = os.getenv("MAINTENANCE_MODE", "false").lower() == "true"
    PER_REFER_AMOUNT: int = int(os.getenv("PER_REFER_AMOUNT", "50"))
    JOINING_BONUS_AMOUNT: int = int(os.getenv("JOINING_BONUS_AMOUNT", "50"))
    USER_DISPLAY_REFERRAL_MAX: str = os.getenv("USER_DISPLAY_REFERRAL_MAX", "100")
    USER_DISPLAY_BONUS_MAX: str = os.getenv("USER_DISPLAY_BONUS_MAX", "100")
    
    # OTP Configuration
    OTP_API_KEY: str = os.getenv("OTP_API_KEY", "")
    OTP_API_URL: str = os.getenv("OTP_API_URL", "https://sms.renflair.in/V1.php")
    
    # Gift Configuration
    GIFT_CHANNEL: str = os.getenv("GIFT_CHANNEL", "")
    GIFT_AMOUNT: int = int(os.getenv("GIFT_AMOUNT", "25"))
    
    # Withdrawal Configuration
    MIN_WITHDRAWAL_AMOUNT: int = int(os.getenv("MIN_WITHDRAWAL_AMOUNT", "100"))
    WITHDRAWAL_AMOUNTS: List[int] = [
        int(amount.strip())
        for amount in os.getenv("WITHDRAWAL_AMOUNTS", "100,200,400,600,800,1000").split(",")
        if amount.strip().isdigit()
    ]
    
    # Security Configuration
    RATE_LIMIT_ENABLED: bool = os.getenv("RATE_LIMIT_ENABLED", "true").lower() == "true"
    MAX_REQUESTS_PER_MINUTE: int = int(os.getenv("MAX_REQUESTS_PER_MINUTE", "30"))
    
    # Timezone
    TIMEZONE: str = os.getenv("TIMEZONE", "Asia/Kolkata")
    
    # Logging
    LOG_LEVEL: str = os.getenv("LOG_LEVEL", "INFO")
    LOG_FILE: str = os.getenv("LOG_FILE", "bot.log")
    
    # Message Templates (matching PHP version)
    WELCOME_MESSAGE_TEMPLATE: str = """🎁 Make Money Easily! Get upto ₹100!

🔺 <a href="https://t.me/{channel}">Click & Join Our Channel</a>

🔷 Must Join Our Channels Before Clicking On [💰GET MONEY💰]"""
    
    INVITATION_MESSAGE_TEMPLATE: str = """🎉 Invite your friends to get money!

Per Invite You Get Upto ₹100

🔗your invitation link(👇️Click to copy)

<code>✨Join me and get upto ₹100
{referral_link}</code>"""
    
    # Constants (matching PHP version)
    BANNED_TEXT: str = "<i>🚫 You are banned from using this bot.</i>"
    MAINTENANCE_TEXT: str = "🛠️ We're currently under maintenance.\n\nPlease try again later. For enquiries, contact our support:\n🔗 @instantohelpbot"
    
    @classmethod
    def validate(cls) -> bool:
        """Validate required settings"""
        required_fields = [
            "BOT_TOKEN",
            "BOT_USERNAME", 
            "MONGODB_URI"
        ]
        
        for field in required_fields:
            if not getattr(cls, field):
                raise ValueError(f"Required setting {field} is not configured")
        
        if not cls.ADMIN_IDS:
            raise ValueError("At least one admin ID must be configured")
        
        return True

# Global settings instance
settings = Settings()

# Validate settings on import
try:
    settings.validate()
except ValueError as e:
    print(f"Configuration error: {e}")
    print("Please check your .env file and ensure all required settings are configured.")
    exit(1)
