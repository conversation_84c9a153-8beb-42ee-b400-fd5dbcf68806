#!/usr/bin/env python3
"""
Production User Migration Script: PHP JSON to Python MongoDB
Migrates user data from PHP bot's JSON files to Python bot's MongoDB database

Usage:
    python migrate_php_users.py --user-id 2027123358
    python migrate_php_users.py --all-users
    python migrate_php_users.py --batch-size 100
"""

import json
import asyncio
import sys
import os
import argparse
from datetime import datetime
from typing import Dict, Any, Optional, List

# Add the python directory to the path
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.append(current_dir)

from config.database import get_collection, COLLECTIONS
from models.user import UserModel
from utils.helpers import get_current_timestamp

class PHPUserMigrator:
    """Production-ready PHP to MongoDB user migrator"""
    
    def __init__(self, php_data_path: str = "../data/users.json"):
        self.php_data_path = php_data_path
        self.migration_stats = {
            'total_users': 0,
            'successful_migrations': 0,
            'failed_migrations': 0,
            'updated_users': 0,
            'new_users': 0,
            'errors': []
        }
    
    def extract_user_from_json(self, target_user_id: int) -> Optional[Dict[str, Any]]:
        """Extract specific user data from large PHP JSON file"""
        try:
            with open(self.php_data_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # Find user pattern
            user_pattern = f'"{target_user_id}":'
            start_pos = content.find(user_pattern)
            
            if start_pos == -1:
                return None
            
            # Find opening brace
            brace_start = content.find('{', start_pos)
            if brace_start == -1:
                return None
            
            # Count braces to find matching closing brace
            brace_count = 0
            pos = brace_start
            in_string = False
            escape_next = False
            
            while pos < len(content):
                char = content[pos]
                
                if escape_next:
                    escape_next = False
                elif char == '\\':
                    escape_next = True
                elif char == '"' and not escape_next:
                    in_string = not in_string
                elif not in_string:
                    if char == '{':
                        brace_count += 1
                    elif char == '}':
                        brace_count -= 1
                        if brace_count == 0:
                            user_json = content[brace_start:pos + 1]
                            return json.loads(user_json)
                
                pos += 1
            
            return None
            
        except Exception as e:
            self.migration_stats['errors'].append(f"Extract user {target_user_id}: {e}")
            return None
    
    def convert_php_to_mongodb(self, php_data: Dict[str, Any]) -> Dict[str, Any]:
        """Convert PHP user data to MongoDB format"""
        # Handle referred_by conversion
        referred_by = php_data.get('referred_by', 'None')
        if referred_by == 'None' or referred_by is None:
            referred_by = 'None'
        else:
            referred_by = str(referred_by)
        
        # Create MongoDB user structure
        mongodb_user = {
            # Basic information
            'user_id': int(php_data.get('user_id')),
            'first_name': php_data.get('first_name', ''),
            'last_name': php_data.get('last_name', ''),
            'username': php_data.get('username', ''),
            
            # Status
            'banned': bool(php_data.get('banned', False)),
            'referred': bool(php_data.get('referred', False)),
            'referred_by': referred_by,
            
            # Financial data
            'balance': int(php_data.get('balance', 0)),
            'joining_bonus_got': int(php_data.get('joining_bonus_got', 0)),
            'successful_withdraw': int(php_data.get('successful_withdraw', 0)),
            'withdraw_under_review': int(php_data.get('withdraw_under_review', 0)),
            
            # Other data
            'gift_claimed': bool(php_data.get('gift_claimed', False)),
            'claimed_levels': php_data.get('claimed_levels', []),
            'referral_link': php_data.get('referral_link', ''),
            
            # Timestamps
            'created_at': get_current_timestamp(),
            'updated_at': get_current_timestamp()
        }
        
        # Convert account_info
        php_account = php_data.get('account_info', {})
        mongodb_user['account_info'] = {
            'name': php_account.get('name', ''),
            'ifsc': php_account.get('ifsc', ''),
            'email': php_account.get('email', ''),
            'account_number': php_account.get('account_number', ''),
            'mobile_number': php_account.get('mobile_number', ''),
            'withdrawal_method': php_account.get('withdrawal_method', ''),
            'usdt_address': php_account.get('usdt_address', ''),
            'binance_id': php_account.get('binance_id', '')
        }
        
        # Convert promotion_report
        php_referrals = php_data.get('promotion_report', [])
        mongodb_user['promotion_report'] = []
        
        for referral in php_referrals:
            mongodb_user['promotion_report'].append({
                'referred_user_name': referral.get('referred_user_name', ''),
                'referred_user_id': int(referral.get('referred_user_id', 0)),
                'amount_got': int(referral.get('amount_got', 0))
            })
        
        # Convert withdrawal_report to withdrawal_reports
        php_withdrawals = php_data.get('withdrawal_report', [])
        mongodb_user['withdrawal_reports'] = []
        
        for withdrawal in php_withdrawals:
            mongodb_user['withdrawal_reports'].append({
                'amount': int(withdrawal.get('amount', 0)),
                'status': withdrawal.get('status', ''),
                'date': withdrawal.get('date', ''),
                'method': withdrawal.get('method', ''),
                'timestamp': withdrawal.get('timestamp', get_current_timestamp())
            })
        
        return mongodb_user
    
    async def migrate_single_user(self, user_id: int) -> bool:
        """Migrate a single user"""
        try:
            print(f"🔄 Migrating user {user_id}...")
            
            # Extract PHP data
            php_data = self.extract_user_from_json(user_id)
            if not php_data:
                print(f"❌ User {user_id} not found in PHP data")
                self.migration_stats['failed_migrations'] += 1
                return False
            
            # Convert to MongoDB format
            mongodb_data = self.convert_php_to_mongodb(php_data)
            
            # Insert/update in MongoDB
            users_collection = await get_collection(COLLECTIONS['users'])
            existing_user = await users_collection.find_one({'user_id': user_id})
            
            if existing_user:
                # Update existing user
                result = await users_collection.replace_one(
                    {'user_id': user_id},
                    mongodb_data
                )
                if result.modified_count > 0:
                    print(f"✅ User {user_id} updated successfully")
                    self.migration_stats['updated_users'] += 1
                else:
                    print(f"ℹ️ User {user_id} data unchanged")
                    self.migration_stats['updated_users'] += 1
            else:
                # Insert new user
                result = await users_collection.insert_one(mongodb_data)
                if result.inserted_id:
                    print(f"✅ User {user_id} migrated successfully")
                    self.migration_stats['new_users'] += 1
                else:
                    print(f"❌ Failed to insert user {user_id}")
                    self.migration_stats['failed_migrations'] += 1
                    return False
            
            self.migration_stats['successful_migrations'] += 1
            return True
            
        except Exception as e:
            print(f"❌ Error migrating user {user_id}: {e}")
            self.migration_stats['errors'].append(f"User {user_id}: {e}")
            self.migration_stats['failed_migrations'] += 1
            return False
    
    def get_all_user_ids(self) -> List[int]:
        """Extract all user IDs from PHP JSON file"""
        try:
            print("🔍 Extracting all user IDs from PHP data...")
            
            with open(self.php_data_path, 'r', encoding='utf-8') as f:
                users_data = json.load(f)
            
            user_ids = [int(user_id) for user_id in users_data.keys()]
            print(f"📊 Found {len(user_ids)} users in PHP data")
            
            return user_ids
            
        except Exception as e:
            print(f"❌ Error extracting user IDs: {e}")
            return []
    
    async def migrate_all_users(self, batch_size: int = 50) -> bool:
        """Migrate all users in batches"""
        try:
            user_ids = self.get_all_user_ids()
            if not user_ids:
                print("❌ No users found to migrate")
                return False
            
            self.migration_stats['total_users'] = len(user_ids)
            
            print(f"🚀 Starting migration of {len(user_ids)} users in batches of {batch_size}")
            
            # Process in batches
            for i in range(0, len(user_ids), batch_size):
                batch = user_ids[i:i + batch_size]
                batch_num = (i // batch_size) + 1
                total_batches = (len(user_ids) + batch_size - 1) // batch_size
                
                print(f"\n📦 Processing batch {batch_num}/{total_batches} ({len(batch)} users)")
                
                for user_id in batch:
                    await self.migrate_single_user(user_id)
                
                # Progress update
                progress = (i + len(batch)) / len(user_ids) * 100
                print(f"📈 Progress: {progress:.1f}% ({i + len(batch)}/{len(user_ids)} users)")
            
            return True
            
        except Exception as e:
            print(f"❌ Error in batch migration: {e}")
            return False
    
    def print_migration_summary(self):
        """Print migration summary"""
        print("\n" + "="*80)
        print("📊 MIGRATION SUMMARY")
        print("="*80)
        
        stats = self.migration_stats
        
        print(f"📈 STATISTICS:")
        print(f"   Total Users: {stats['total_users']:,}")
        print(f"   Successful Migrations: {stats['successful_migrations']:,}")
        print(f"   Failed Migrations: {stats['failed_migrations']:,}")
        print(f"   New Users: {stats['new_users']:,}")
        print(f"   Updated Users: {stats['updated_users']:,}")
        
        if stats['total_users'] > 0:
            success_rate = (stats['successful_migrations'] / stats['total_users']) * 100
            print(f"   Success Rate: {success_rate:.1f}%")
        
        if stats['errors']:
            print(f"\n❌ ERRORS ({len(stats['errors'])}):")
            for i, error in enumerate(stats['errors'][:10], 1):  # Show first 10 errors
                print(f"   {i}. {error}")
            
            if len(stats['errors']) > 10:
                print(f"   ... and {len(stats['errors']) - 10} more errors")
        
        if stats['successful_migrations'] == stats['total_users']:
            print(f"\n🎉 ALL MIGRATIONS SUCCESSFUL!")
        elif stats['successful_migrations'] > 0:
            print(f"\n✅ MIGRATION COMPLETED WITH SOME ISSUES")
        else:
            print(f"\n❌ MIGRATION FAILED")
        
        print("="*80)

async def main():
    """Main migration function"""
    parser = argparse.ArgumentParser(description='Migrate PHP user data to MongoDB')
    parser.add_argument('--user-id', type=int, help='Migrate specific user ID')
    parser.add_argument('--all-users', action='store_true', help='Migrate all users')
    parser.add_argument('--batch-size', type=int, default=50, help='Batch size for all users migration')
    parser.add_argument('--php-data', type=str, default='../data/users.json', help='Path to PHP users.json file')
    
    args = parser.parse_args()
    
    if not args.user_id and not args.all_users:
        print("❌ Please specify either --user-id or --all-users")
        return 1
    
    migrator = PHPUserMigrator(args.php_data)
    
    print("🚀 PHP to MongoDB User Migration")
    print("="*50)
    print(f"📁 PHP Data File: {args.php_data}")
    print(f"📅 Migration Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    success = False
    
    if args.user_id:
        print(f"👤 Migrating single user: {args.user_id}")
        migrator.migration_stats['total_users'] = 1
        success = await migrator.migrate_single_user(args.user_id)
    elif args.all_users:
        print(f"👥 Migrating all users (batch size: {args.batch_size})")
        success = await migrator.migrate_all_users(args.batch_size)
    
    # Print summary
    migrator.print_migration_summary()
    
    return 0 if success else 1

if __name__ == "__main__":
    try:
        exit_code = asyncio.run(main())
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\n⚠️ Migration interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ Migration failed with error: {e}")
        sys.exit(1)
