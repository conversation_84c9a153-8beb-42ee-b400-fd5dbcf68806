"""
Gift Code data models
Maintains identical structure to PHP JSON format
"""

import re
from typing import Dict, List, Any, Optional
from utils.helpers import get_current_timestamp, get_current_date

class GiftCodeModel:
    """Gift code data model matching PHP structure exactly"""
    
    @staticmethod
    def create_gift_code(
        code: str,
        amount: float,
        usage_limit: int,
        created_by: int,
        expiry_date: int = 0
    ) -> Dict[str, Any]:
        """Create gift code data structure (matching PHP exactly)"""
        
        return {
            "code": code,
            "amount": amount,
            "usage_limit": usage_limit,
            "expiry_date": expiry_date,
            "created_by": created_by,
            "created_at": get_current_timestamp(),
            "used_count": 0,
            "last_used": 0,
            "redeemed_by": []
        }
    
    @staticmethod
    def validate_gift_code_format(code: str) -> Dict[str, Any]:
        """Validate gift code format (matching PHP validation exactly)"""
        errors = []
        
        if not code or not isinstance(code, str):
            errors.append("Code cannot be empty")
            return {"valid": False, "errors": errors}
        
        # Check format: letters and numbers only (matching PHP regex)
        if not re.match(r'^[A-Za-z0-9]+$', code):
            errors.append("Invalid code format. Use only letters and numbers.")
        
        # Check length: 3-20 characters (matching PHP validation)
        if len(code) < 3 or len(code) > 20:
            errors.append("Code must be between 3 and 20 characters.")
        
        return {"valid": len(errors) == 0, "errors": errors}
    
    @staticmethod
    def validate_gift_amount(amount: str) -> Dict[str, Any]:
        """Validate gift amount (matching PHP validation exactly)"""
        errors = []
        
        try:
            amount_float = float(amount)
            if amount_float <= 0:
                errors.append("Invalid amount. Please enter a positive number.")
            elif amount_float > 10000:  # Reasonable upper limit
                errors.append("Amount too high. Maximum allowed is ₹10000.")
        except (ValueError, TypeError):
            errors.append("Invalid amount. Please enter a positive number.")
        
        return {"valid": len(errors) == 0, "errors": errors, "amount": amount_float if len(errors) == 0 else 0}
    
    @staticmethod
    def validate_usage_limit(limit_text: str) -> Dict[str, Any]:
        """Validate usage limit (matching PHP validation exactly)"""
        errors = []
        usage_limit = 0  # 0 means unlimited
        
        if limit_text.lower().strip() == 'unlimited':
            usage_limit = 0
        else:
            try:
                limit_int = int(limit_text)
                if limit_int <= 0:
                    errors.append("Invalid limit. Enter a positive number or \"unlimited\".")
                else:
                    usage_limit = limit_int
            except (ValueError, TypeError):
                errors.append("Invalid limit. Enter a positive number or \"unlimited\".")
        
        return {"valid": len(errors) == 0, "errors": errors, "usage_limit": usage_limit}
    
    @staticmethod
    def check_code_redemption_eligibility(
        gift_code: Dict[str, Any], 
        user_id: int
    ) -> Dict[str, Any]:
        """Check if user can redeem gift code (matching PHP logic exactly)"""
        
        # Check if code is expired
        if (gift_code.get('expiry_date', 0) > 0 and 
            get_current_timestamp() > gift_code['expiry_date']):
            return {
                'eligible': False, 
                'message': 'Gift code has expired.'
            }
        
        # Check if user has already redeemed this code (handle both traditional and admin gift codes)
        redeemed_by = gift_code.get('redeemed_by', [])
        users_claimed = gift_code.get('users_claimed', [])  # Admin gift codes use this field

        if user_id in redeemed_by or user_id in users_claimed:
            return {
                'eligible': False,
                'message': 'You have already redeemed this gift code.'
            }

        # Check usage limit (handle both traditional and admin gift codes)
        usage_limit = gift_code.get('usage_limit', 0)
        used_count = gift_code.get('used_count', 0)
        usage_count = gift_code.get('usage_count', 0)  # Admin gift codes use this field

        # Use the appropriate count field
        current_usage = max(used_count, usage_count)

        if usage_limit > 0 and current_usage >= usage_limit:
            return {
                'eligible': False,
                'message': 'Gift code usage limit reached.'
            }
        
        return {'eligible': True, 'message': 'Code is valid for redemption'}
    
    @staticmethod
    def format_gift_code_generation_step1_message() -> str:
        """Format gift code generation step 1 message (matching PHP exactly)"""
        message = "🎫 <b>Generate Gift Code</b>\n\n"
        message += "Let's create a new gift code for users to redeem.\n\n"
        message += "📝 <b>Step 1:</b> Enter the gift code text\n"
        message += "Choose a unique code (letters and numbers only).\n\n"
        message += "Send /cancel to cancel the process."
        
        return message
    
    @staticmethod
    def format_gift_code_generation_step2_message(code: str) -> str:
        """Format gift code generation step 2 message (matching PHP exactly)"""
        message = "🎫 <b>Generate Gift Code</b>\n\n"
        message += f"📝 <b>Code:</b> {code}\n\n"
        message += "💰 <b>Step 2:</b> Enter the gift amount (in ₹)\n"
        message += "Enter only the number (e.g., 50 for ₹50).\n\n"
        message += "Send /cancel to cancel the process."
        
        return message
    
    @staticmethod
    def format_gift_code_generation_step3_message(code: str, amount: float) -> str:
        """Format gift code generation step 3 message (matching PHP exactly)"""
        message = "🎫 <b>Generate Gift Code</b>\n\n"
        message += f"📝 <b>Code:</b> {code}\n"
        message += f"💰 <b>Amount:</b> ₹{amount}\n\n"
        message += "🔢 <b>Step 3:</b> Enter usage limit (optional)\n"
        message += "How many times can this code be used?\n"
        message += "Send \"unlimited\" for no limit or a number.\n\n"
        message += "Send /cancel to cancel the process."
        
        return message
    
    @staticmethod
    def format_gift_code_creation_success_message(
        code: str, 
        amount: float, 
        usage_limit: int
    ) -> str:
        """Format gift code creation success message (matching PHP exactly)"""
        message = "✅ <b>Gift Code Created Successfully!</b>\n\n"
        message += f"📝 <b>Code:</b> <code>{code}</code>\n"
        message += f"💰 <b>Amount:</b> ₹{amount}\n"
        message += f"🔢 <b>Usage Limit:</b> " + ("Unlimited" if usage_limit == 0 else str(usage_limit)) + "\n\n"
        message += "Users can now redeem this code in the Extra Rewards section!"
        
        return message
    
    @staticmethod
    def format_redeem_gift_code_message() -> str:
        """Format redeem gift code message (matching PHP exactly)"""
        message = "🎫 <b>Redeem Gift Code</b>\n\n"
        message += "Enter your gift code to redeem rewards:\n\n"
        message += "Send /cancel to cancel the process."
        
        return message
    
    @staticmethod
    def format_gift_code_redemption_success_message(amount: float) -> str:
        """Format gift code redemption success message"""
        message = "*🎉 Bonus claimed successfully!*\n"
        message += "You're all set. Keep earning with Instanto."

        return message
    
    @staticmethod
    def format_gift_code_redemption_failure_message(error_message: str) -> str:
        """Format gift code redemption failure message (matching PHP exactly)"""
        message = "❌ <b>Redemption Failed</b>\n\n"
        message += error_message
        
        return message
    
    @staticmethod
    def format_admin_gift_codes_list_message(gift_codes: List[Dict[str, Any]]) -> str:
        """Format admin gift codes list message"""
        if not gift_codes:
            message = "🎫 <b>Gift Codes Management</b>\n\n"
            message += "❌ No gift codes found.\n\n"
            message += "Create your first gift code using the Generate button below."
            return message
        
        message = "🎫 <b>Gift Codes Management</b>\n\n"
        message += f"📊 <b>Total Codes:</b> {len(gift_codes)}\n\n"
        
        # Show recent codes (last 10)
        recent_codes = sorted(gift_codes, key=lambda x: x.get('created_at', 0), reverse=True)[:10]
        
        for i, code in enumerate(recent_codes, 1):
            usage_limit = code.get('usage_limit', 0)
            used_count = code.get('used_count', 0)
            amount = code.get('amount', 0)
            
            status = "🟢 Active"
            if usage_limit > 0 and used_count >= usage_limit:
                status = "🔴 Exhausted"
            elif code.get('expiry_date', 0) > 0 and get_current_timestamp() > code['expiry_date']:
                status = "⏰ Expired"
            
            usage_text = f"{used_count}/" + ("∞" if usage_limit == 0 else str(usage_limit))
            
            message += f"<b>{i}.</b> <code>{code['code']}</code>\n"
            message += f"   💰 ₹{amount} | 📊 {usage_text} | {status}\n\n"
        
        if len(gift_codes) > 10:
            message += f"... and {len(gift_codes) - 10} more codes\n\n"
        
        message += "💡 <b>Tip:</b> Use Generate to create new codes"
        
        return message
    
    @staticmethod
    def create_gift_code_generation_success_keyboard():
        """Create gift code generation success keyboard (matching PHP exactly)"""
        from telegram import InlineKeyboardButton, InlineKeyboardMarkup
        
        keyboard_buttons = [
            [InlineKeyboardButton('🎫 Generate Another Code', callback_data='generateGiftCode')],
            [InlineKeyboardButton('↩️ Back to Admin Panel', callback_data='admin')]
        ]
        
        return InlineKeyboardMarkup(keyboard_buttons)
    
    @staticmethod
    def create_gift_code_redemption_success_keyboard():
        """Create gift code redemption success keyboard (matching PHP exactly)"""
        from telegram import InlineKeyboardButton, InlineKeyboardMarkup
        
        keyboard_buttons = [
            [InlineKeyboardButton('💰 My Wallet', callback_data='myWallet')],
            [InlineKeyboardButton('🎁 Extra Rewards', callback_data='extraRewards')]
        ]
        
        return InlineKeyboardMarkup(keyboard_buttons)
    
    @staticmethod
    def create_admin_gift_codes_keyboard():
        """Create admin gift codes management keyboard"""
        from telegram import InlineKeyboardButton, InlineKeyboardMarkup
        
        keyboard_buttons = [
            [InlineKeyboardButton('🎫 Generate Gift Code', callback_data='generateGiftCode')],
            [InlineKeyboardButton('📊 View All Codes', callback_data='viewAllGiftCodes')],
            [InlineKeyboardButton('↩️ Back to Admin Panel', callback_data='admin')]
        ]
        
        return InlineKeyboardMarkup(keyboard_buttons)
    
    @staticmethod
    def get_gift_code_statistics(gift_codes: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Get gift code statistics"""
        if not gift_codes:
            return {
                'total_codes': 0,
                'active_codes': 0,
                'expired_codes': 0,
                'exhausted_codes': 0,
                'total_amount': 0,
                'total_redeemed': 0,
                'total_redemptions': 0
            }
        
        current_time = get_current_timestamp()
        active_codes = 0
        expired_codes = 0
        exhausted_codes = 0
        total_amount = 0
        total_redeemed = 0
        total_redemptions = 0
        
        for code in gift_codes:
            amount = code.get('amount', 0)
            usage_limit = code.get('usage_limit', 0)
            used_count = code.get('used_count', 0)
            expiry_date = code.get('expiry_date', 0)
            
            total_amount += amount
            total_redeemed += amount * used_count
            total_redemptions += used_count
            
            # Determine status
            if expiry_date > 0 and current_time > expiry_date:
                expired_codes += 1
            elif usage_limit > 0 and used_count >= usage_limit:
                exhausted_codes += 1
            else:
                active_codes += 1
        
        return {
            'total_codes': len(gift_codes),
            'active_codes': active_codes,
            'expired_codes': expired_codes,
            'exhausted_codes': exhausted_codes,
            'total_amount': total_amount,
            'total_redeemed': total_redeemed,
            'total_redemptions': total_redemptions
        }
