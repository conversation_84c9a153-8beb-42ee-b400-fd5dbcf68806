#!/usr/bin/env python3
"""
Data Migration Script: PHP JSON to Python MongoDB
Migrates user data from PHP bot's JSON files to Python bot's MongoDB database
Focus: User ID 2027123358 as test case
"""

import json
import asyncio
import sys
import os
from datetime import datetime
from typing import Dict, Any, Optional
from pprint import pprint

# Add the python directory to the path
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.append(current_dir)

from config.database import get_collection, COLLECTIONS
from models.user import UserModel
from utils.helpers import get_current_timestamp

class UserDataMigrator:
    """Handles migration of user data from PHP JSON to MongoDB"""
    
    def __init__(self):
        self.target_user_id = 2027123358
        self.php_data_path = "../data/users.json"
        self.original_user_data = None
        self.migrated_user_data = None
        
    async def load_php_user_data(self) -> Optional[Dict[str, Any]]:
        """Load specific user data from PHP JSON file"""
        try:
            print(f"🔍 Loading PHP user data for user ID: {self.target_user_id}")
            
            with open(self.php_data_path, 'r', encoding='utf-8') as f:
                users_data = json.load(f)
            
            # Find the target user
            user_key = str(self.target_user_id)
            if user_key in users_data:
                self.original_user_data = users_data[user_key]
                print(f"✅ Found user data for {self.target_user_id}")
                return self.original_user_data
            else:
                print(f"❌ User {self.target_user_id} not found in PHP data")
                return None
                
        except FileNotFoundError:
            print(f"❌ PHP data file not found: {self.php_data_path}")
            return None
        except json.JSONDecodeError as e:
            print(f"❌ Error parsing PHP JSON data: {e}")
            return None
        except Exception as e:
            print(f"❌ Error loading PHP user data: {e}")
            return None
    
    def convert_php_to_mongodb_format(self, php_user_data: Dict[str, Any]) -> Dict[str, Any]:
        """Convert PHP user data structure to MongoDB format"""
        print("🔄 Converting PHP data structure to MongoDB format...")
        
        # Extract basic user information
        user_id = int(php_user_data.get('user_id', 0))
        first_name = php_user_data.get('first_name', '')
        last_name = php_user_data.get('last_name', '')
        username = php_user_data.get('username', '')
        
        # Handle referred_by conversion
        referred_by = php_user_data.get('referred_by', 'None')
        if referred_by == 'None' or referred_by is None:
            referred_by = 'None'
        else:
            referred_by = str(referred_by)
        
        # Create base user structure using UserModel
        mongodb_user = UserModel.create_new_user(
            user_id=user_id,
            first_name=first_name,
            last_name=last_name,
            username=username,
            referred_by=referred_by
        )
        
        # Update with PHP data values
        mongodb_user.update({
            'banned': bool(php_user_data.get('banned', False)),
            'referred': bool(php_user_data.get('referred', False)),
            'joining_bonus_got': int(php_user_data.get('joining_bonus_got', 0)),
            'referral_link': php_user_data.get('referral_link', ''),
            'balance': int(php_user_data.get('balance', 0)),
            'successful_withdraw': int(php_user_data.get('successful_withdraw', 0)),
            'withdraw_under_review': int(php_user_data.get('withdraw_under_review', 0)),
            'gift_claimed': bool(php_user_data.get('gift_claimed', False)),
            'claimed_levels': php_user_data.get('claimed_levels', [])
        })
        
        # Convert account_info
        php_account_info = php_user_data.get('account_info', {})
        mongodb_user['account_info'] = {
            'name': php_account_info.get('name', ''),
            'ifsc': php_account_info.get('ifsc', ''),
            'email': php_account_info.get('email', ''),
            'account_number': php_account_info.get('account_number', ''),
            'mobile_number': php_account_info.get('mobile_number', ''),
            'withdrawal_method': php_account_info.get('withdrawal_method', ''),
            'usdt_address': php_account_info.get('usdt_address', ''),
            'binance_id': php_account_info.get('binance_id', '')
        }
        
        # Convert promotion_report (referral data)
        php_promotion_report = php_user_data.get('promotion_report', [])
        mongodb_user['promotion_report'] = []
        
        for referral in php_promotion_report:
            mongodb_user['promotion_report'].append({
                'referred_user_name': referral.get('referred_user_name', ''),
                'referred_user_id': int(referral.get('referred_user_id', 0)),
                'amount_got': int(referral.get('amount_got', 0))
            })
        
        # Convert withdrawal_report (note: PHP uses 'withdrawal_report', Python uses 'withdrawal_reports')
        php_withdrawal_report = php_user_data.get('withdrawal_report', [])
        mongodb_user['withdrawal_reports'] = []
        
        for withdrawal in php_withdrawal_report:
            mongodb_user['withdrawal_reports'].append({
                'amount': int(withdrawal.get('amount', 0)),
                'status': withdrawal.get('status', ''),
                'date': withdrawal.get('date', ''),
                'method': withdrawal.get('method', ''),
                'timestamp': withdrawal.get('timestamp', get_current_timestamp())
            })
        
        print("✅ Data structure conversion completed")
        return mongodb_user
    
    async def migrate_user_to_mongodb(self, user_data: Dict[str, Any]) -> bool:
        """Insert migrated user data into MongoDB"""
        try:
            print("💾 Inserting user data into MongoDB...")
            
            users_collection = await get_collection(COLLECTIONS['users'])
            
            # Check if user already exists
            existing_user = await users_collection.find_one({'user_id': user_data['user_id']})
            
            if existing_user:
                print(f"⚠️ User {user_data['user_id']} already exists in MongoDB")
                print("🔄 Updating existing user data...")
                
                # Update existing user
                result = await users_collection.replace_one(
                    {'user_id': user_data['user_id']},
                    user_data
                )
                
                if result.modified_count > 0:
                    print("✅ User data updated successfully")
                    return True
                else:
                    print("⚠️ No changes made to existing user data")
                    return True
            else:
                # Insert new user
                result = await users_collection.insert_one(user_data)
                
                if result.inserted_id:
                    print(f"✅ User {user_data['user_id']} migrated successfully")
                    return True
                else:
                    print("❌ Failed to insert user data")
                    return False
                    
        except Exception as e:
            print(f"❌ Error migrating user to MongoDB: {e}")
            return False
    
    async def validate_migration(self) -> Dict[str, Any]:
        """Validate migrated data against original PHP data"""
        print("\n🔍 Validating migration...")
        
        try:
            # Fetch migrated user from MongoDB
            users_collection = await get_collection(COLLECTIONS['users'])
            self.migrated_user_data = await users_collection.find_one({'user_id': self.target_user_id})
            
            if not self.migrated_user_data:
                return {
                    'success': False,
                    'error': 'Migrated user not found in MongoDB'
                }
            
            validation_results = {
                'success': True,
                'user_id_match': self.original_user_data['user_id'] == self.migrated_user_data['user_id'],
                'basic_info_match': True,
                'financial_data_match': True,
                'referral_data_match': True,
                'account_info_match': True,
                'discrepancies': []
            }
            
            # Validate basic information
            basic_fields = ['first_name', 'username', 'banned', 'referred', 'referred_by']
            for field in basic_fields:
                php_value = self.original_user_data.get(field)
                mongo_value = self.migrated_user_data.get(field)
                
                # Handle type conversions
                if field in ['banned', 'referred']:
                    php_value = bool(php_value)
                
                if php_value != mongo_value:
                    validation_results['basic_info_match'] = False
                    validation_results['discrepancies'].append(f"{field}: PHP={php_value}, MongoDB={mongo_value}")
            
            # Validate financial data
            financial_fields = ['balance', 'successful_withdraw', 'withdraw_under_review', 'joining_bonus_got']
            for field in financial_fields:
                php_value = int(self.original_user_data.get(field, 0))
                mongo_value = int(self.migrated_user_data.get(field, 0))
                
                if php_value != mongo_value:
                    validation_results['financial_data_match'] = False
                    validation_results['discrepancies'].append(f"{field}: PHP={php_value}, MongoDB={mongo_value}")
            
            # Validate referral data
            php_referrals = self.original_user_data.get('promotion_report', [])
            mongo_referrals = self.migrated_user_data.get('promotion_report', [])
            
            if len(php_referrals) != len(mongo_referrals):
                validation_results['referral_data_match'] = False
                validation_results['discrepancies'].append(f"Referral count: PHP={len(php_referrals)}, MongoDB={len(mongo_referrals)}")
            
            # Calculate total referral earnings
            php_total_earnings = sum(int(r.get('amount_got', 0)) for r in php_referrals)
            mongo_total_earnings = sum(int(r.get('amount_got', 0)) for r in mongo_referrals)
            
            if php_total_earnings != mongo_total_earnings:
                validation_results['referral_data_match'] = False
                validation_results['discrepancies'].append(f"Total referral earnings: PHP={php_total_earnings}, MongoDB={mongo_total_earnings}")
            
            # Validate account information
            php_account = self.original_user_data.get('account_info', {})
            mongo_account = self.migrated_user_data.get('account_info', {})
            
            account_fields = ['name', 'ifsc', 'email', 'account_number', 'mobile_number', 'withdrawal_method', 'usdt_address']
            for field in account_fields:
                php_value = php_account.get(field, '')
                mongo_value = mongo_account.get(field, '')
                
                if php_value != mongo_value:
                    validation_results['account_info_match'] = False
                    validation_results['discrepancies'].append(f"account_info.{field}: PHP='{php_value}', MongoDB='{mongo_value}'")
            
            return validation_results
            
        except Exception as e:
            return {
                'success': False,
                'error': f'Validation error: {e}'
            }
    
    def print_comparison_report(self, validation_results: Dict[str, Any]):
        """Print detailed comparison report"""
        print("\n" + "="*80)
        print("📊 MIGRATION VALIDATION REPORT")
        print("="*80)
        
        if not validation_results['success']:
            print(f"❌ Validation failed: {validation_results.get('error', 'Unknown error')}")
            return
        
        print(f"👤 User ID: {self.target_user_id}")
        print(f"✅ User ID Match: {validation_results['user_id_match']}")
        print(f"✅ Basic Info Match: {validation_results['basic_info_match']}")
        print(f"✅ Financial Data Match: {validation_results['financial_data_match']}")
        print(f"✅ Referral Data Match: {validation_results['referral_data_match']}")
        print(f"✅ Account Info Match: {validation_results['account_info_match']}")
        
        if validation_results['discrepancies']:
            print(f"\n⚠️ Found {len(validation_results['discrepancies'])} discrepancies:")
            for i, discrepancy in enumerate(validation_results['discrepancies'], 1):
                print(f"   {i}. {discrepancy}")
        else:
            print("\n🎉 No discrepancies found! Migration is perfect.")
        
        # Print summary statistics
        if self.original_user_data and self.migrated_user_data:
            print(f"\n📈 SUMMARY STATISTICS:")
            print(f"   Balance: ₹{self.migrated_user_data.get('balance', 0)}")
            print(f"   Total Referrals: {len(self.migrated_user_data.get('promotion_report', []))}")
            
            total_earnings = sum(int(r.get('amount_got', 0)) for r in self.migrated_user_data.get('promotion_report', []))
            print(f"   Total Referral Earnings: ₹{total_earnings}")
            print(f"   Successful Withdrawals: ₹{self.migrated_user_data.get('successful_withdraw', 0)}")
            print(f"   Under Review: ₹{self.migrated_user_data.get('withdraw_under_review', 0)}")
            
            account_info = self.migrated_user_data.get('account_info', {})
            withdrawal_method = account_info.get('withdrawal_method', 'Not set')
            print(f"   Withdrawal Method: {withdrawal_method}")
        
        print("="*80)
    
    async def run_migration(self):
        """Execute the complete migration process"""
        print("🚀 Starting User Data Migration Process")
        print("="*50)
        
        # Step 1: Load PHP user data
        php_user_data = await self.load_php_user_data()
        if not php_user_data:
            print("❌ Migration failed: Could not load PHP user data")
            return False
        
        # Step 2: Convert to MongoDB format
        mongodb_user_data = self.convert_php_to_mongodb_format(php_user_data)
        
        # Step 3: Migrate to MongoDB
        migration_success = await self.migrate_user_to_mongodb(mongodb_user_data)
        if not migration_success:
            print("❌ Migration failed: Could not insert data into MongoDB")
            return False
        
        # Step 4: Validate migration
        validation_results = await self.validate_migration()
        
        # Step 5: Print comparison report
        self.print_comparison_report(validation_results)
        
        return validation_results['success'] if validation_results['success'] else False

async def main():
    """Main migration function"""
    migrator = UserDataMigrator()
    success = await migrator.run_migration()
    
    if success:
        print("\n🎉 Migration completed successfully!")
        return 0
    else:
        print("\n❌ Migration failed!")
        return 1

if __name__ == "__main__":
    try:
        exit_code = asyncio.run(main())
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\n⚠️ Migration interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ Migration failed with error: {e}")
        sys.exit(1)
