2025-07-14 02:06:34,379 - __main__ - INFO - Received signal 2, initiating shutdown...
2025-07-14 02:06:34,379 - __main__ - INFO - Shutting down bot...
2025-07-14 02:06:34,944 - apscheduler.scheduler - INFO - Scheduler has been shut down
2025-07-14 02:06:34,979 - config.database - INFO - Disconnected from MongoDB
2025-07-14 02:06:34,980 - __main__ - INFO - Bot shutdown completed
2025-07-14 02:06:41,789 - __main__ - INFO - Connecting to MongoDB...
2025-07-14 02:06:50,365 - config.database - INFO - Successfully connected to MongoDB: referral_bot
2025-07-14 02:06:52,820 - config.database - INFO - Database collections and indexes initialized successfully
2025-07-14 02:06:52,821 - __main__ - INFO - Initializing Telegram bot...
2025-07-14 02:06:54,065 - __main__ - INFO - All handlers registered successfully
2025-07-14 02:06:54,850 - services.admin_service - INFO - Initialized admin settings for 4 admins
2025-07-14 02:06:54,889 - __main__ - INFO - Bot info updated: @raferrobot (Refer Earn Bot)
2025-07-14 02:06:54,890 - __main__ - INFO - Admin settings and level rewards system initialized
2025-07-14 02:06:54,891 - __main__ - INFO - Bot initialization completed successfully
2025-07-14 02:06:54,891 - __main__ - INFO - Starting bot polling...
2025-07-14 02:06:55,077 - apscheduler.scheduler - INFO - Scheduler started
2025-07-14 02:06:55,267 - __main__ - INFO - Bot is now running and polling for updates...
2025-07-14 02:07:14,951 - handlers.session_handlers - INFO - DEBUG: handle_session_message called for user 2027123358, step: gift_broadcast_forward
2025-07-14 02:07:19,752 - handlers.session_handlers - INFO - DEBUG: handle_session_message called for user 2027123358, step: gift_broadcast_reward
2025-07-14 02:12:48,959 - __main__ - INFO - Received signal 2, initiating shutdown...
2025-07-14 02:12:48,960 - __main__ - INFO - Shutting down bot...
2025-07-14 02:12:52,618 - apscheduler.scheduler - INFO - Scheduler has been shut down
2025-07-14 02:12:52,655 - config.database - INFO - Disconnected from MongoDB
2025-07-14 02:12:52,656 - __main__ - INFO - Bot shutdown completed
2025-07-14 02:13:02,885 - __main__ - INFO - Connecting to MongoDB...
2025-07-14 02:13:11,772 - config.database - INFO - Successfully connected to MongoDB: referral_bot
2025-07-14 02:13:14,203 - config.database - INFO - Database collections and indexes initialized successfully
2025-07-14 02:13:14,204 - __main__ - INFO - Initializing Telegram bot...
2025-07-14 02:13:15,690 - __main__ - INFO - All handlers registered successfully
2025-07-14 02:13:16,506 - services.admin_service - INFO - Initialized admin settings for 4 admins
2025-07-14 02:13:16,539 - __main__ - INFO - Bot info updated: @raferrobot (Refer Earn Bot)
2025-07-14 02:13:16,540 - __main__ - INFO - Admin settings and level rewards system initialized
2025-07-14 02:13:16,540 - __main__ - INFO - Bot initialization completed successfully
2025-07-14 02:13:16,541 - __main__ - INFO - Starting bot polling...
2025-07-14 02:13:16,734 - apscheduler.scheduler - INFO - Scheduler started
2025-07-14 02:13:16,926 - __main__ - INFO - Bot is now running and polling for updates...
2025-07-14 02:24:17,377 - __main__ - INFO - Received signal 2, initiating shutdown...
2025-07-14 02:24:17,377 - __main__ - INFO - Shutting down bot...
2025-07-14 02:24:17,977 - apscheduler.scheduler - INFO - Scheduler has been shut down
2025-07-14 02:24:18,010 - config.database - INFO - Disconnected from MongoDB
2025-07-14 02:24:18,011 - __main__ - INFO - Bot shutdown completed
2025-07-14 02:24:27,877 - __main__ - INFO - Connecting to MongoDB...
2025-07-14 02:24:36,675 - config.database - INFO - Successfully connected to MongoDB: referral_bot
2025-07-14 02:24:39,175 - config.database - INFO - Database collections and indexes initialized successfully
2025-07-14 02:24:39,176 - __main__ - INFO - Initializing Telegram bot...
2025-07-14 02:24:40,433 - __main__ - INFO - All handlers registered successfully
2025-07-14 02:24:41,290 - services.admin_service - INFO - Initialized admin settings for 4 admins
2025-07-14 02:24:41,324 - __main__ - INFO - Bot info updated: @raferrobot (Refer Earn Bot)
2025-07-14 02:24:41,325 - __main__ - INFO - Admin settings and level rewards system initialized
2025-07-14 02:24:41,326 - __main__ - INFO - Bot initialization completed successfully
2025-07-14 02:24:41,326 - __main__ - INFO - Starting bot polling...
2025-07-14 02:24:41,528 - apscheduler.scheduler - INFO - Scheduler started
2025-07-14 02:24:41,733 - __main__ - INFO - Bot is now running and polling for updates...
2025-07-14 02:30:58,482 - handlers.session_handlers - INFO - === SESSION MESSAGE HANDLER for user **********, step: broadcast_media ===
2025-07-14 02:30:58,483 - handlers.session_handlers - INFO - Session data: {'_id': ObjectId('687418f008c5dcffe6df96de'), 'user_id': **********, 'created_at': **********, 'data': {}, 'step': 'broadcast_media', 'updated_at': **********}
2025-07-14 02:30:58,484 - handlers.session_handlers - INFO - Message has text: False
2025-07-14 02:30:58,484 - handlers.session_handlers - INFO - Message has photo: True
2025-07-14 02:30:58,485 - handlers.session_handlers - INFO - Message has video: False
2025-07-14 02:30:58,486 - handlers.session_handlers - INFO - Message has document: False
2025-07-14 02:30:58,486 - handlers.session_handlers - INFO - Message text: 'None'
2025-07-14 02:30:58,487 - handlers.session_handlers - INFO - === ROUTING TO BROADCAST MEDIA HANDLER for user ********** ===
2025-07-14 02:30:58,487 - handlers.session_handlers - INFO - === BROADCAST MEDIA PROCESSING START for user ********** ===
2025-07-14 02:30:58,488 - handlers.session_handlers - INFO - Message object exists: True
2025-07-14 02:30:58,489 - handlers.session_handlers - INFO - Message ID: 3720
2025-07-14 02:30:58,489 - handlers.session_handlers - INFO - Message text: None
2025-07-14 02:30:58,496 - handlers.session_handlers - INFO - Message caption: None
2025-07-14 02:30:58,496 - handlers.session_handlers - INFO - === CHECKING MEDIA TYPES for user ********** ===
2025-07-14 02:30:58,497 - handlers.session_handlers - INFO - Message has photo: True
2025-07-14 02:30:58,497 - handlers.session_handlers - INFO - Message has video: False
2025-07-14 02:30:58,498 - handlers.session_handlers - INFO - Message has document: False
2025-07-14 02:30:58,498 - handlers.session_handlers - INFO - Message has audio: False
2025-07-14 02:30:58,499 - handlers.session_handlers - INFO - Message has voice: False
2025-07-14 02:30:58,499 - handlers.session_handlers - INFO - Message has sticker: False
2025-07-14 02:30:58,500 - handlers.session_handlers - INFO - Message has animation: False
2025-07-14 02:30:58,501 - handlers.session_handlers - INFO - Message has video_note: False
2025-07-14 02:30:58,501 - handlers.session_handlers - INFO - ✅ PHOTO detected - file_id: AgACAgUAAxkBAAIN62h0DkehT3766t66H26RBEk-1xaJAAKkxTEbDk-hV7pcT1Wk-OrbAQADAgADeQADNgQ
2025-07-14 02:30:58,503 - handlers.session_handlers - INFO - === MEDIA PROCESSING SUCCESS for user ********** ===
2025-07-14 02:30:58,504 - handlers.session_handlers - INFO - Media data created: {'type': 'photo', 'file_id': 'AgACAgUAAxkBAAIN62h0DkehT3766t66H26RBEk-1xaJAAKkxTEbDk-hV7pcT1Wk-OrbAQADAgADeQADNgQ'}
2025-07-14 02:30:58,504 - handlers.session_handlers - INFO - === SAVING TO BROADCAST DRAFT for user ********** ===
2025-07-14 02:30:58,525 - handlers.session_handlers - INFO - Getting existing broadcast draft for user **********
2025-07-14 02:30:58,559 - handlers.session_handlers - INFO - Existing draft: {}
2025-07-14 02:30:58,560 - handlers.session_handlers - INFO - Updated draft with media: {'media': {'type': 'photo', 'file_id': 'AgACAgUAAxkBAAIN62h0DkehT3766t66H26RBEk-1xaJAAKkxTEbDk-hV7pcT1Wk-OrbAQADAgADeQADNgQ'}}
2025-07-14 02:30:58,561 - handlers.session_handlers - INFO - Calling save_broadcast_draft for user **********
2025-07-14 02:30:58,561 - services.broadcast_service - INFO - === SAVING BROADCAST DRAFT for admin ********** ===
2025-07-14 02:30:58,562 - services.broadcast_service - INFO - Data to save: {'media': {'type': 'photo', 'file_id': 'AgACAgUAAxkBAAIN62h0DkehT3766t66H26RBEk-1xaJAAKkxTEbDk-hV7pcT1Wk-OrbAQADAgADeQADNgQ'}}
2025-07-14 02:30:58,563 - services.broadcast_service - INFO - Got collection: AsyncIOMotorCollection(Collection(Database(MongoClient(host=['devcluster-shard-00-01.fwm8r.mongodb.net:27017', 'devcluster-shard-00-00.fwm8r.mongodb.net:27017', 'devcluster-shard-00-02.fwm8r.mongodb.net:27017'], document_class=dict, tz_aware=False, connect=False, retrywrites=True, w='majority', authsource='admin', replicaset='atlas-ydx7ih-shard-0', tls=True, maxpoolsize=50, minpoolsize=10, maxidletimems=30000, serverselectiontimeoutms=5000, connecttimeoutms=10000, sockettimeoutms=20000, driver=DriverInfo(name='Motor', version='3.3.2', platform='asyncio')), 'referral_bot'), 'broadcast_sessions'))
2025-07-14 02:30:58,564 - services.broadcast_service - INFO - Attempting to update/upsert broadcast draft for admin **********
2025-07-14 02:30:58,605 - services.broadcast_service - INFO - === BROADCAST DRAFT SAVE RESULT for admin ********** ===
2025-07-14 02:30:58,606 - services.broadcast_service - INFO - Acknowledged: True
2025-07-14 02:30:58,606 - services.broadcast_service - INFO - Matched count: 0
2025-07-14 02:30:58,607 - services.broadcast_service - INFO - Modified count: 0
2025-07-14 02:30:58,607 - services.broadcast_service - INFO - Upserted ID: 68741e8908c5dcffe6df9763
2025-07-14 02:30:58,607 - services.broadcast_service - INFO - Returning success: True
2025-07-14 02:30:58,608 - handlers.session_handlers - INFO - === BROADCAST DRAFT SAVE RESULT for user **********: True ===
2025-07-14 02:30:58,608 - handlers.session_handlers - INFO - Clearing session for user **********
2025-07-14 02:30:58,646 - handlers.session_handlers - INFO - Session cleared for user **********
2025-07-14 02:30:58,647 - handlers.session_handlers - INFO - === SENDING SUCCESS MESSAGE to user ********** ===
2025-07-14 02:30:59,023 - handlers.session_handlers - INFO - ✅ SUCCESS MESSAGE SENT to user **********
2025-07-14 02:30:59,023 - handlers.session_handlers - INFO - === BROADCAST MEDIA PROCESSING END for user ********** ===
2025-07-14 02:31:39,336 - handlers.session_handlers - INFO - === SESSION MESSAGE HANDLER for user **********, step: broadcast_text ===
2025-07-14 02:31:39,337 - handlers.session_handlers - INFO - Session data: {'_id': ObjectId('68741eb008c5dcffe6df9765'), 'user_id': **********, 'created_at': 1752440497, 'data': {}, 'step': 'broadcast_text', 'updated_at': 1752440497}
2025-07-14 02:31:39,338 - handlers.session_handlers - INFO - Message has text: True
2025-07-14 02:31:39,338 - handlers.session_handlers - INFO - Message has photo: False
2025-07-14 02:31:39,340 - handlers.session_handlers - INFO - Message has video: False
2025-07-14 02:31:39,340 - handlers.session_handlers - INFO - Message has document: False
2025-07-14 02:31:39,340 - handlers.session_handlers - INFO - Message text: 'hhi'
2025-07-14 02:31:39,374 - services.broadcast_service - INFO - === SAVING BROADCAST DRAFT for admin ********** ===
2025-07-14 02:31:39,375 - services.broadcast_service - INFO - Data to save: {'_id': ObjectId('68741e8908c5dcffe6df9763'), 'admin_id': **********, 'status': 'draft', 'media': {'type': 'photo', 'file_id': 'AgACAgUAAxkBAAIN62h0DkehT3766t66H26RBEk-1xaJAAKkxTEbDk-hV7pcT1Wk-OrbAQADAgADeQADNgQ'}, 'updated_at': 1752440458, 'text': 'hhi'}
2025-07-14 02:31:39,376 - services.broadcast_service - INFO - Got collection: AsyncIOMotorCollection(Collection(Database(MongoClient(host=['devcluster-shard-00-01.fwm8r.mongodb.net:27017', 'devcluster-shard-00-00.fwm8r.mongodb.net:27017', 'devcluster-shard-00-02.fwm8r.mongodb.net:27017'], document_class=dict, tz_aware=False, connect=False, retrywrites=True, w='majority', authsource='admin', replicaset='atlas-ydx7ih-shard-0', tls=True, maxpoolsize=50, minpoolsize=10, maxidletimems=30000, serverselectiontimeoutms=5000, connecttimeoutms=10000, sockettimeoutms=20000, driver=DriverInfo(name='Motor', version='3.3.2', platform='asyncio')), 'referral_bot'), 'broadcast_sessions'))
2025-07-14 02:31:39,377 - services.broadcast_service - INFO - Attempting to update/upsert broadcast draft for admin **********
2025-07-14 02:31:39,414 - services.broadcast_service - INFO - === BROADCAST DRAFT SAVE RESULT for admin ********** ===
2025-07-14 02:31:39,415 - services.broadcast_service - INFO - Acknowledged: True
2025-07-14 02:31:39,415 - services.broadcast_service - INFO - Matched count: 1
2025-07-14 02:31:39,416 - services.broadcast_service - INFO - Modified count: 1
2025-07-14 02:31:39,416 - services.broadcast_service - INFO - Upserted ID: None
2025-07-14 02:31:39,417 - services.broadcast_service - INFO - Returning success: True
2025-07-14 02:32:15,595 - handlers.admin_handlers - ERROR - Error in handle_confirm_start_broadcast: 'BroadcastService' object has no attribute 'clear_broadcast_draft'
2025-07-14 09:54:18,928 - __main__ - INFO - Connecting to MongoDB...
2025-07-14 09:54:27,864 - config.database - INFO - Successfully connected to MongoDB: referral_bot
2025-07-14 09:54:30,254 - config.database - INFO - Database collections and indexes initialized successfully
2025-07-14 09:54:30,256 - __main__ - INFO - Initializing Telegram bot...
2025-07-14 09:54:31,501 - __main__ - INFO - All handlers registered successfully
2025-07-14 09:54:32,337 - services.admin_service - INFO - Initialized admin settings for 4 admins
2025-07-14 09:54:32,372 - __main__ - INFO - Bot info updated: @raferrobot (Refer Earn Bot)
2025-07-14 09:54:32,373 - __main__ - INFO - Admin settings and level rewards system initialized
2025-07-14 09:54:32,374 - __main__ - INFO - Bot initialization completed successfully
2025-07-14 09:54:32,374 - __main__ - INFO - Starting bot polling...
2025-07-14 09:54:32,568 - apscheduler.scheduler - INFO - Scheduler started
2025-07-14 09:54:32,764 - __main__ - INFO - Bot is now running and polling for updates...
2025-07-14 09:54:35,964 - __main__ - WARNING - Received media update with missing user information
2025-07-14 09:55:21,008 - __main__ - WARNING - Received media update with missing user information
2025-07-14 09:57:21,260 - handlers.session_handlers - INFO - === SESSION MESSAGE HANDLER for user **********, step: broadcast_media ===
2025-07-14 09:57:21,261 - handlers.session_handlers - INFO - Session data: {'_id': ObjectId('6874872408c5dcffe6dfa553'), 'user_id': **********, 'created_at': **********, 'data': {}, 'step': 'broadcast_media', 'updated_at': **********}
2025-07-14 09:57:21,261 - handlers.session_handlers - INFO - Message has text: False
2025-07-14 09:57:21,262 - handlers.session_handlers - INFO - Message has photo: True
2025-07-14 09:57:21,263 - handlers.session_handlers - INFO - Message has video: False
2025-07-14 09:57:21,264 - handlers.session_handlers - INFO - Message has document: False
2025-07-14 09:57:21,264 - handlers.session_handlers - INFO - Message text: 'None'
2025-07-14 09:57:21,265 - handlers.session_handlers - INFO - === ROUTING TO BROADCAST MEDIA HANDLER for user ********** ===
2025-07-14 09:57:21,266 - handlers.session_handlers - INFO - === BROADCAST MEDIA PROCESSING START for user ********** ===
2025-07-14 09:57:21,267 - handlers.session_handlers - INFO - Message object exists: True
2025-07-14 09:57:21,271 - handlers.session_handlers - INFO - Message ID: 3747
2025-07-14 09:57:21,271 - handlers.session_handlers - INFO - Message text: None
2025-07-14 09:57:21,272 - handlers.session_handlers - INFO - Message caption: None
2025-07-14 09:57:21,274 - handlers.session_handlers - INFO - === CHECKING MEDIA TYPES for user ********** ===
2025-07-14 09:57:21,275 - handlers.session_handlers - INFO - Message has photo: True
2025-07-14 09:57:21,276 - handlers.session_handlers - INFO - Message has video: False
2025-07-14 09:57:21,277 - handlers.session_handlers - INFO - Message has document: False
2025-07-14 09:57:21,277 - handlers.session_handlers - INFO - Message has audio: False
2025-07-14 09:57:21,278 - handlers.session_handlers - INFO - Message has voice: False
2025-07-14 09:57:21,280 - handlers.session_handlers - INFO - Message has sticker: False
2025-07-14 09:57:21,282 - handlers.session_handlers - INFO - Message has animation: False
2025-07-14 09:57:21,283 - handlers.session_handlers - INFO - Message has video_note: False
2025-07-14 09:57:21,284 - handlers.session_handlers - INFO - ✅ PHOTO detected - file_id: AgACAgUAAxkBAAIN62h0DkehT3766t66H26RBEk-1xaJAAKkxTEbDk-hV7pcT1Wk-OrbAQADAgADeQADNgQ
2025-07-14 09:57:21,286 - handlers.session_handlers - INFO - === MEDIA PROCESSING SUCCESS for user ********** ===
2025-07-14 09:57:21,286 - handlers.session_handlers - INFO - Media data created: {'type': 'photo', 'file_id': 'AgACAgUAAxkBAAIN62h0DkehT3766t66H26RBEk-1xaJAAKkxTEbDk-hV7pcT1Wk-OrbAQADAgADeQADNgQ'}
2025-07-14 09:57:21,288 - handlers.session_handlers - INFO - === SAVING TO BROADCAST DRAFT for user ********** ===
2025-07-14 09:57:21,330 - handlers.session_handlers - INFO - Getting existing broadcast draft for user **********
2025-07-14 09:57:21,365 - handlers.session_handlers - INFO - Existing draft: {'_id': ObjectId('68741e8908c5dcffe6df9763'), 'admin_id': **********, 'status': 'draft', 'media': {'type': 'photo', 'file_id': 'AgACAgUAAxkBAAIN62h0DkehT3766t66H26RBEk-1xaJAAKkxTEbDk-hV7pcT1Wk-OrbAQADAgADeQADNgQ'}, 'updated_at': 1752440499, 'text': 'hhi'}
2025-07-14 09:57:21,366 - handlers.session_handlers - INFO - Updated draft with media: {'_id': ObjectId('68741e8908c5dcffe6df9763'), 'admin_id': **********, 'status': 'draft', 'media': {'type': 'photo', 'file_id': 'AgACAgUAAxkBAAIN62h0DkehT3766t66H26RBEk-1xaJAAKkxTEbDk-hV7pcT1Wk-OrbAQADAgADeQADNgQ'}, 'updated_at': 1752440499, 'text': 'hhi'}
2025-07-14 09:57:21,366 - handlers.session_handlers - INFO - Calling save_broadcast_draft for user **********
2025-07-14 09:57:21,367 - services.broadcast_service - INFO - === SAVING BROADCAST DRAFT for admin ********** ===
2025-07-14 09:57:21,368 - services.broadcast_service - INFO - Data to save: {'_id': ObjectId('68741e8908c5dcffe6df9763'), 'admin_id': **********, 'status': 'draft', 'media': {'type': 'photo', 'file_id': 'AgACAgUAAxkBAAIN62h0DkehT3766t66H26RBEk-1xaJAAKkxTEbDk-hV7pcT1Wk-OrbAQADAgADeQADNgQ'}, 'updated_at': 1752440499, 'text': 'hhi'}
2025-07-14 09:57:21,369 - services.broadcast_service - INFO - Got collection: AsyncIOMotorCollection(Collection(Database(MongoClient(host=['devcluster-shard-00-00.fwm8r.mongodb.net:27017', 'devcluster-shard-00-02.fwm8r.mongodb.net:27017', 'devcluster-shard-00-01.fwm8r.mongodb.net:27017'], document_class=dict, tz_aware=False, connect=False, retrywrites=True, w='majority', authsource='admin', replicaset='atlas-ydx7ih-shard-0', tls=True, maxpoolsize=50, minpoolsize=10, maxidletimems=30000, serverselectiontimeoutms=5000, connecttimeoutms=10000, sockettimeoutms=20000, driver=DriverInfo(name='Motor', version='3.3.2', platform='asyncio')), 'referral_bot'), 'broadcast_sessions'))
2025-07-14 09:57:21,370 - services.broadcast_service - INFO - Attempting to update/upsert broadcast draft for admin **********
2025-07-14 09:57:21,407 - services.broadcast_service - INFO - === BROADCAST DRAFT SAVE RESULT for admin ********** ===
2025-07-14 09:57:21,408 - services.broadcast_service - INFO - Acknowledged: True
2025-07-14 09:57:21,409 - services.broadcast_service - INFO - Matched count: 1
2025-07-14 09:57:21,410 - services.broadcast_service - INFO - Modified count: 1
2025-07-14 09:57:21,411 - services.broadcast_service - INFO - Upserted ID: None
2025-07-14 09:57:21,412 - services.broadcast_service - INFO - Returning success: True
2025-07-14 09:57:21,413 - handlers.session_handlers - INFO - === BROADCAST DRAFT SAVE RESULT for user **********: True ===
2025-07-14 09:57:21,413 - handlers.session_handlers - INFO - Clearing session for user **********
2025-07-14 09:57:21,450 - handlers.session_handlers - INFO - Session cleared for user **********
2025-07-14 09:57:21,451 - handlers.session_handlers - INFO - === SENDING SUCCESS MESSAGE to user ********** ===
2025-07-14 09:57:21,831 - handlers.session_handlers - INFO - ✅ SUCCESS MESSAGE SENT to user **********
2025-07-14 09:57:21,832 - handlers.session_handlers - INFO - === BROADCAST MEDIA PROCESSING END for user ********** ===
2025-07-14 09:57:44,825 - handlers.session_handlers - INFO - === SESSION MESSAGE HANDLER for user **********, step: broadcast_text ===
2025-07-14 09:57:44,827 - handlers.session_handlers - INFO - Session data: {'_id': ObjectId('6874872d08c5dcffe6dfa556'), 'user_id': **********, 'created_at': 1752467246, 'data': {}, 'step': 'broadcast_text', 'updated_at': 1752467246}
2025-07-14 09:57:44,827 - handlers.session_handlers - INFO - Message has text: True
2025-07-14 09:57:44,827 - handlers.session_handlers - INFO - Message has photo: False
2025-07-14 09:57:44,828 - handlers.session_handlers - INFO - Message has video: False
2025-07-14 09:57:44,828 - handlers.session_handlers - INFO - Message has document: False
2025-07-14 09:57:44,828 - handlers.session_handlers - INFO - Message text: 'fsdlkfjs
sdfdsaf
dgfas'
2025-07-14 09:57:44,861 - services.broadcast_service - INFO - === SAVING BROADCAST DRAFT for admin ********** ===
2025-07-14 09:57:44,861 - services.broadcast_service - INFO - Data to save: {'_id': ObjectId('68741e8908c5dcffe6df9763'), 'admin_id': **********, 'status': 'draft', 'media': {'type': 'photo', 'file_id': 'AgACAgUAAxkBAAIN62h0DkehT3766t66H26RBEk-1xaJAAKkxTEbDk-hV7pcT1Wk-OrbAQADAgADeQADNgQ'}, 'updated_at': 1752467241, 'text': 'fsdlkfjs\nsdfdsaf\ndgfas'}
2025-07-14 09:57:44,863 - services.broadcast_service - INFO - Got collection: AsyncIOMotorCollection(Collection(Database(MongoClient(host=['devcluster-shard-00-00.fwm8r.mongodb.net:27017', 'devcluster-shard-00-02.fwm8r.mongodb.net:27017', 'devcluster-shard-00-01.fwm8r.mongodb.net:27017'], document_class=dict, tz_aware=False, connect=False, retrywrites=True, w='majority', authsource='admin', replicaset='atlas-ydx7ih-shard-0', tls=True, maxpoolsize=50, minpoolsize=10, maxidletimems=30000, serverselectiontimeoutms=5000, connecttimeoutms=10000, sockettimeoutms=20000, driver=DriverInfo(name='Motor', version='3.3.2', platform='asyncio')), 'referral_bot'), 'broadcast_sessions'))
2025-07-14 09:57:44,863 - services.broadcast_service - INFO - Attempting to update/upsert broadcast draft for admin **********
2025-07-14 09:57:44,900 - services.broadcast_service - INFO - === BROADCAST DRAFT SAVE RESULT for admin ********** ===
2025-07-14 09:57:44,901 - services.broadcast_service - INFO - Acknowledged: True
2025-07-14 09:57:44,901 - services.broadcast_service - INFO - Matched count: 1
2025-07-14 09:57:44,902 - services.broadcast_service - INFO - Modified count: 1
2025-07-14 09:57:44,902 - services.broadcast_service - INFO - Upserted ID: None
2025-07-14 09:57:44,902 - services.broadcast_service - INFO - Returning success: True
2025-07-14 09:57:59,702 - handlers.session_handlers - INFO - === SESSION MESSAGE HANDLER for user **********, step: broadcast_buttons ===
2025-07-14 09:57:59,704 - handlers.session_handlers - INFO - Session data: {'_id': ObjectId('6874874508c5dcffe6dfa55b'), 'user_id': **********, 'created_at': 1752467270, 'data': {}, 'step': 'broadcast_buttons', 'updated_at': 1752467270}
2025-07-14 09:57:59,704 - handlers.session_handlers - INFO - Message has text: True
2025-07-14 09:57:59,705 - handlers.session_handlers - INFO - Message has photo: False
2025-07-14 09:57:59,705 - handlers.session_handlers - INFO - Message has video: False
2025-07-14 09:57:59,705 - handlers.session_handlers - INFO - Message has document: False
2025-07-14 09:57:59,706 - handlers.session_handlers - INFO - Message text: 'Visit Website | https://example.com
Join Channel | https://t.me/channel
Join Channel | https://t.me/channel'
2025-07-14 09:58:16,762 - handlers.session_handlers - INFO - === SESSION MESSAGE HANDLER for user **********, step: broadcast_buttons ===
2025-07-14 09:58:16,763 - handlers.session_handlers - INFO - Session data: {'_id': ObjectId('6874874508c5dcffe6dfa55b'), 'user_id': **********, 'created_at': 1752467270, 'data': {}, 'step': 'broadcast_buttons', 'updated_at': 1752467270}
2025-07-14 09:58:16,763 - handlers.session_handlers - INFO - Message has text: True
2025-07-14 09:58:16,764 - handlers.session_handlers - INFO - Message has photo: False
2025-07-14 09:58:16,764 - handlers.session_handlers - INFO - Message has video: False
2025-07-14 09:58:16,765 - handlers.session_handlers - INFO - Message has document: False
2025-07-14 09:58:16,765 - handlers.session_handlers - INFO - Message text: 'Join Channel | https://t.me/channel
Join Channel | https://t.me/channel'
2025-07-14 09:59:03,784 - handlers.session_handlers - INFO - === SESSION MESSAGE HANDLER for user **********, step: broadcast_buttons ===
2025-07-14 09:59:03,794 - handlers.session_handlers - INFO - Session data: {'_id': ObjectId('6874874508c5dcffe6dfa55b'), 'user_id': **********, 'created_at': 1752467270, 'data': {}, 'step': 'broadcast_buttons', 'updated_at': 1752467270}
2025-07-14 09:59:03,796 - handlers.session_handlers - INFO - Message has text: True
2025-07-14 09:59:03,806 - handlers.session_handlers - INFO - Message has photo: False
2025-07-14 09:59:03,820 - handlers.session_handlers - INFO - Message has video: False
2025-07-14 09:59:03,828 - handlers.session_handlers - INFO - Message has document: False
2025-07-14 09:59:03,834 - handlers.session_handlers - INFO - Message text: 'Join Channel | https://t.me/channel'
2025-07-14 09:59:08,581 - handlers.session_handlers - INFO - === SESSION MESSAGE HANDLER for user **********, step: broadcast_buttons ===
2025-07-14 09:59:08,582 - handlers.session_handlers - INFO - Session data: {'_id': ObjectId('6874874508c5dcffe6dfa55b'), 'user_id': **********, 'created_at': 1752467270, 'data': {}, 'step': 'broadcast_buttons', 'updated_at': 1752467270}
2025-07-14 09:59:08,582 - handlers.session_handlers - INFO - Message has text: True
2025-07-14 09:59:08,583 - handlers.session_handlers - INFO - Message has photo: False
2025-07-14 09:59:08,583 - handlers.session_handlers - INFO - Message has video: False
2025-07-14 09:59:08,584 - handlers.session_handlers - INFO - Message has document: False
2025-07-14 09:59:08,584 - handlers.session_handlers - INFO - Message text: 'Join Channel | https://t.me/channel'
2025-07-14 10:04:49,302 - __main__ - WARNING - Received update with missing user or message information
2025-07-14 10:04:57,524 - __main__ - WARNING - Received update with missing user or message information
2025-07-14 10:06:05,950 - __main__ - WARNING - Received update with missing user or message information
2025-07-14 10:06:23,963 - __main__ - WARNING - Received update with missing user or message information
2025-07-14 10:06:58,371 - __main__ - INFO - Received signal 2, initiating shutdown...
2025-07-14 10:06:58,372 - __main__ - INFO - Shutting down bot...
2025-07-14 10:06:58,679 - apscheduler.scheduler - INFO - Scheduler has been shut down
2025-07-14 10:06:58,720 - config.database - INFO - Disconnected from MongoDB
2025-07-14 10:06:58,720 - __main__ - INFO - Bot shutdown completed
