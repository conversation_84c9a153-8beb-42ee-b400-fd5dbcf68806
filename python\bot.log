2025-07-14 12:46:06,904 - __main__ - INFO - Connecting to MongoDB...
2025-07-14 12:46:16,333 - config.database - INFO - Successfully connected to MongoDB: referral_bot
2025-07-14 12:46:19,714 - config.database - INFO - Database collections and indexes initialized successfully
2025-07-14 12:46:19,716 - __main__ - INFO - Initializing Telegram bot...
2025-07-14 12:46:20,945 - __main__ - INFO - All handlers registered successfully
2025-07-14 12:46:21,819 - services.admin_service - INFO - Initialized admin settings for 4 admins
2025-07-14 12:46:21,854 - __main__ - INFO - Bot info updated: @raferrobot (Refer <PERSON>arn <PERSON>)
2025-07-14 12:46:21,854 - __main__ - INFO - Admin settings and level rewards system initialized
2025-07-14 12:46:21,854 - __main__ - INFO - Bot initialization completed successfully
2025-07-14 12:46:21,856 - __main__ - INFO - Starting bot polling...
2025-07-14 12:46:22,059 - apscheduler.scheduler - INFO - Scheduler started
2025-07-14 12:46:22,265 - __main__ - INFO - Bot is now running and polling for updates...
2025-07-14 12:55:02,715 - services.user_service - INFO - Created new user: 8153676253 (Titanium Bots)
2025-07-14 12:55:05,251 - handlers.user_handlers - ERROR - Error sending new user notification: Forbidden: bot is not a member of the channel chat
