2025-07-14 12:46:06,904 - __main__ - INFO - Connecting to MongoDB...
2025-07-14 12:46:16,333 - config.database - INFO - Successfully connected to MongoDB: referral_bot
2025-07-14 12:46:19,714 - config.database - INFO - Database collections and indexes initialized successfully
2025-07-14 12:46:19,716 - __main__ - INFO - Initializing Telegram bot...
2025-07-14 12:46:20,945 - __main__ - INFO - All handlers registered successfully
2025-07-14 12:46:21,819 - services.admin_service - INFO - Initialized admin settings for 4 admins
2025-07-14 12:46:21,854 - __main__ - INFO - Bot info updated: @raferrobot (Refer <PERSON>arn <PERSON>)
2025-07-14 12:46:21,854 - __main__ - INFO - Admin settings and level rewards system initialized
2025-07-14 12:46:21,854 - __main__ - INFO - Bot initialization completed successfully
2025-07-14 12:46:21,856 - __main__ - INFO - Starting bot polling...
2025-07-14 12:46:22,059 - apscheduler.scheduler - INFO - Scheduler started
2025-07-14 12:46:22,265 - __main__ - INFO - Bot is now running and polling for updates...
2025-07-14 12:55:02,715 - services.user_service - INFO - Created new user: 8153676253 (Titanium Bots)
2025-07-14 12:55:05,251 - handlers.user_handlers - ERROR - Error sending new user notification: Forbidden: bot is not a member of the channel chat
2025-07-14 13:30:25,399 - __main__ - INFO - Connecting to MongoDB...
2025-07-14 13:30:34,566 - config.database - INFO - Successfully connected to MongoDB: referral_bot
2025-07-14 13:30:37,072 - config.database - INFO - Database collections and indexes initialized successfully
2025-07-14 13:30:37,073 - __main__ - INFO - Initializing Telegram bot...
2025-07-14 13:30:38,287 - __main__ - INFO - All handlers registered successfully
2025-07-14 13:30:39,173 - services.admin_service - INFO - Initialized admin settings for 4 admins
2025-07-14 13:30:57,785 - services.admin_service - ERROR - Error cleaning up stale broadcast sessions: ac-bac3qqk-shard-00-01.egi1fti.mongodb.net:27017: connection closed (configured timeouts: socketTimeoutMS: 20000.0ms, connectTimeoutMS: 10000.0ms)
2025-07-14 13:30:57,795 - __main__ - INFO - Bot info updated: @raferrobot (Refer Earn Bot)
2025-07-14 13:30:57,804 - __main__ - INFO - Admin settings and level rewards system initialized
2025-07-14 13:30:57,817 - __main__ - INFO - Bot initialization completed successfully
2025-07-14 13:30:57,833 - __main__ - INFO - Starting bot polling...
2025-07-14 13:31:02,858 - __main__ - ERROR - Error during polling: Timed out
2025-07-14 13:31:02,859 - __main__ - INFO - Shutting down bot...
2025-07-14 13:31:02,860 - __main__ - ERROR - Error during shutdown: This Updater is not running!
2025-07-14 13:31:02,860 - __main__ - ERROR - Unexpected error: Timed out
